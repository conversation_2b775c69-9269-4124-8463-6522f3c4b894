{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../src/cli.ts"], "names": [], "mappings": ";;;AAEA,+BAAgE;AAChE,6CAA4D;AAC5D,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,sBAAQ,CAAC;AACzC,uDAAkD;AAGlD,SAAS,UAAU,CACjB,IAAY,EACZ,OAA6B,EAC7B,MAAgB,EAChB,GAAW;IAEX,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClB,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAEjC,IACE,CAAC,MAAM;QACP,CAAC,MAAM,CAAC,OAAO;QACf,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;YACvB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC/B,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,CAAC,EAC5B,CAAC;QACD,OAAO;IACT,CAAC;IAED,KAAK,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;QAClC,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;IAC3C,CAAC;AACH,CAAC;AAED,KAAK,UAAU,GAAG,CAChB,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EACxB,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAC5B,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAC3B,SAAS,GAAG,MAAM,EAClB,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE;IAEnB,MAAM,IAAI,GAAG;QACX,EAAE,EAAE,IAAI;QACR,IAAI,EAAE,GAAG;QACT,YAAY,EAAE,IAAI;QAClB,GAAG,EAAE,MAAM,IAAI,OAAO,IAAI,MAAM,IAAI,OAAO;KAC5C,CAAC;IACF,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,IAAA,+BAAa,EACtE,CAAC,UAAU,CAAC,EACZ,IAAI,CACL,CAAC;IACF,MAAM,QAAQ,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAC/D,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,IAAI,MAAM,KAAK,OAAO,EAAE,CAAC;QACvB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClB,IAAI,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACzB,KAAK,IAAI,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC7B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;IACH,CAAC;SAAM,IAAI,MAAM,KAAK,OAAO,EAAE,CAAC;QAC9B,MAAM,EAAE,CAAC,IAAA,WAAI,EAAC,GAAG,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QACjE,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE,CAAC;YACzB,MAAM,GAAG,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACzB,MAAM,IAAI,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;YACrC,MAAM,GAAG,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,CAAC;YAC1B,MAAM,KAAK,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACtC,MAAM,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;SAAM,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;QAC7B,MAAM,cAAc,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,KAAK,CAAC;QAC5D,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE,CAAC;YACzB,MAAM,KAAK,GAAG,IAAA,uBAAS,EAAC,CAAC,CAAC,CAAC;YAC3B,IAAI,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/B,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC;YACtB,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,GAAG,IAAA,sBAAQ,EAAC,CAAC,CAAC,CAAC;gBACzB,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC;YACrB,CAAC;QACH,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,cAAc,CAAC,CAAC;IACtC,CAAC;SAAM,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QACD,MAAM,mBAAmB,GAAG,CAC1B,IAAA,iBAAU,EAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAA,eAAQ,EAAC,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAC7D,CAAC,OAAO,CAAC,QAAQ,EAAE,UAAG,CAAC,CAAC;QAEzB,UAAU,CAAC,mBAAmB,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;IACxD,CAAC;SAAM,CAAC;QACN,MAAM,CAAC,IAAI,CAAC,SAAS,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3D,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChB,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QACxC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACzB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChB,MAAM,CAAC,IAAI,CACT,kEAAkE,CACnE,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QACrE,MAAM,CAAC,IAAI,CAAC,2DAA2D,CAAC,CAAC;QACzE,MAAM,CAAC,IAAI,CACT,yEAAyE,CAC1E,CAAC;IACJ,CAAC;IACD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC;AAED,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/C,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC"}