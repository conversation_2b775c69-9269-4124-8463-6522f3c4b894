<!DOCTYPE html><html lang="zh-CN"> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="description" content="领创工作室专注于数码科技领域，提供原创软件工具、技术教程和创意内容，涵盖编程、设计、摄影、音乐等多个方面。"><meta name="keywords" content="领创工作室,领创,LACS,领创科技,数码科技创新,原创软件工具,编程教程,设计资源,摄影作品,音乐创作"><meta property="og:title" content="领创工作室-官方网站-lacs.cc"><meta property="og:description" content="领创工作室专注于数码科技领域，提供原创软件工具、技术教程和创意内容，涵盖编程、设计、摄影、音乐等多个方面。"><meta property="og:image" content="https://gitee.com/lacsgf/img/raw/master/webp/bg-lacs-group.webp"><meta name="robots" content="index, follow"><link rel="canonical" href="https://lacs.cc/"><!-- 引入Font Awesome --><link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet"><!-- 引入粒子效果库 --><script type="module" src="/assets/Layout.astro_astro_type_script_index_0_lang.BE6OMyjS.js"></script><link rel="icon" href="/img/favicon.ico" type="image/x-icon"><title>领创工作室-官方网站-lacs.cc</title><link rel="stylesheet" href="/assets/hout.CD17msai.css"><script>window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };
		var script = document.createElement('script');
		script.defer = true;
		script.src = '/_vercel/insights/script.js';
		var head = document.querySelector('head');
		head.appendChild(script);
	</script></head> <body class="font-inter bg-gray-50 text-gray-800">  <!-- 导航栏 --><nav class="fixed top-0 left-0 right-0 z-50 transition-all duration-500 nav-default bg-white/80 backdrop-blur-sm p-4" id="main-nav"> <div class="container mx-auto flex justify-between items-center"> <a class="flex items-center group" href="/"> <div class="relative overflow-hidden rounded-full p-0.5 mr-2 bg-gradient-to-r from-primary-400 to-secondary-400 animate-pulse-slow"> <img src="/img/lacs.webp" class="h-8 w-8 rounded-full object-cover transform group-hover:scale-110 transition-transform duration-300" alt="领创工作室Logo"> </div> <span class="text-xl font-bold gradient-text">领创工作室</span> </a> <button class="navbar-toggler md:hidden text-gray-700 focus:outline-none hover:text-gray-900 transition-colors duration-300" type="button" id="menu-toggle" aria-label="切换菜单"> <i class="fas fa-bars text-xl"></i> </button> <div class="hidden md:flex items-center space-x-8" id="navbar-menu"> <a href="/" class="relative text-gray-700 hover:text-gray-900 transition-colors duration-300 group"> <span>首页</span> <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gray-900 group-hover:w-full transition-all duration-300"></span> </a> <a href="#projects-section" class="relative text-gray-700 hover:text-gray-900 transition-colors duration-300 group"> <span>项目</span> <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gray-900 group-hover:w-full transition-all duration-300"></span> </a> <a href="#sites-section" class="relative text-gray-700 hover:text-gray-900 transition-colors duration-300 group"> <span>分站</span> <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gray-900 group-hover:w-full transition-all duration-300"></span> </a> <a href="#about" class="relative text-gray-700 hover:text-gray-900 transition-colors duration-300 group"> <span>关于我们</span> <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gray-900 group-hover:w-full transition-all duration-300"></span> </a> <button class="btn-dark-mode text-gray-700 hover:text-gray-900 transition-all duration-300 transform hover:rotate-12" id="dark-mode-toggle" aria-label="切换深色模式"> <i class="fas fa-moon text-xl"></i> </button> </div> </div> <!-- 移动端菜单 --> <div class="md:hidden bg-white/80 backdrop-blur-sm hidden transition-all duration-500 border-t border-gray-200 p-4" id="mobile-menu"> <div class="container mx-auto flex flex-col space-y-4"> <a href="/" class="text-gray-700 hover:text-gray-900 hover:pl-2 transition-all duration-300 py-2 border-b border-gray-200"> <i class="fas fa-home mr-2 text-primary-400"></i> 首页
</a> <a href="#projects-section" class="text-gray-700 hover:text-gray-900 hover:pl-2 transition-all duration-300 py-2 border-b border-gray-200"> <i class="fas fa-project-diagram mr-2 text-primary-400"></i> 项目
</a> <a href="#sites-section" class="text-gray-700 hover:text-gray-900 hover:pl-2 transition-all duration-300 py-2 border-b border-gray-200"> <i class="fas fa-sitemap mr-2 text-primary-400"></i> 分站
</a> <a href="#about" class="text-gray-700 hover:text-gray-900 hover:pl-2 transition-all duration-300 py-2 border-b border-gray-200"> <i class="fas fa-info-circle mr-2 text-primary-400"></i> 关于我们
</a> <button class="flex items-center text-gray-700 hover:text-gray-900 hover:pl-2 transition-all duration-300 py-2" id="mobile-dark-mode-toggle" aria-label="切换深色模式"> <i class="fas fa-moon mr-2 text-primary-400"></i> 深色模式
</button> </div> </div> </nav>  <section class="pt-28 pb-16 min-h-screen flex items-center relative overflow-hidden"> <!-- 粒子背景 --> <div id="particles-js" class="absolute inset-0 z-0"></div> <!-- 背景动效元素 --> <div class="absolute inset-0 overflow-hidden pointer-events-none"> <div class="absolute -top-10 -left-10 w-64 h-64 bg-primary-400/10 rounded-full blur-3xl animate-pulse"></div> <div class="absolute top-1/4 right-0 w-96 h-96 bg-secondary-400/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div> <div class="absolute bottom-20 left-1/3 w-80 h-80 bg-accent-400/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div> <!-- 科技感网格背景 --> <div class="absolute inset-0 bg-grid-pattern opacity-5"></div> <!-- 装饰线条 --> <div class="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary-400/30 to-transparent"></div> <div class="absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-secondary-400/30 to-transparent"></div> </div> <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10"> <div class="flex flex-col md:flex-row items-center justify-center"> <!-- 科技感装饰元素 --> <div class="md:w-1/2 flex justify-center mt-10 md:mt-0 relative perspective-1000"> <img src="/img/bg-lacs-group.webp" alt="领创工作室团队展示" class="max-w-full h-auto rounded-xl shadow-neon-primary animate-float object-cover transform transition-all duration-700 hover:scale-105 hover:shadow-neon-primary" style="max-height: 600px;"> <!-- 添加科技感装饰元素 --> <div class="absolute -bottom-6 -left-6 w-24 h-24 bg-primary/10 rounded-full blur-2xl"></div> <div class="absolute -top-6 -right-6 w-32 h-32 bg-secondary/10 rounded-full blur-2xl"></div> </div> <div class="md:w-1/2 mb-8 md:mb-0 transform transition-all duration-700 hover:translate-y-[-10px] md:pl-6 lg:pl-10 xl:pl-14 md:pr-2 lg:pr-4 xl:pr-6"> <h1 class="text-[clamp(2rem,5vw,3.5rem)] font-bold text-dark leading-tight mb-2"> <span class="gradient-text">领创工作室</span><br> <span class="typing-effect text-primary-600 inline-block">Lead And Create Studio</span> </h1> <p class="mt-4 text-lg text-gray-600 max-w-xl opacity-90 hover:opacity-100 transition-opacity duration-300 border-l-4 border-primary-400/30 pl-4">
我们致力于开发高质量的原创软件和工具，分享数码科技知识，为您带来更好的数字体验。
</p> <div class="mt-8 flex flex-wrap gap-4"> <a href="#projects-section" class="btn-primary px-6 py-3 rounded-lg flex items-center gap-2 shadow-neon transform hover:-translate-y-1 transition-all duration-300"> <i class="fas fa-rocket"></i> 探索项目
</a> <a href="#about" class="btn-secondary px-6 py-3 rounded-lg flex items-center gap-2 transform hover:-translate-y-1 transition-all duration-300"> <i class="fas fa-info-circle"></i> 关于我们
</a> </div> <!-- 技术标签 --> <div class="mt-8 flex flex-wrap gap-2"> <span class="bg-primary-50 text-primary-700 text-xs px-3 py-1 rounded-full border border-primary-100">软件开发</span> <span class="bg-secondary-50 text-secondary-700 text-xs px-3 py-1 rounded-full border border-secondary-100">数码科技</span> <span class="bg-accent-50 text-accent-700 text-xs px-3 py-1 rounded-full border border-accent-100">创意设计</span> <span class="bg-gray-50 text-gray-700 text-xs px-3 py-1 rounded-full border border-gray-100">技术分享</span> </div> </div> </div> </div> <!-- 向下滚动指示器 --> <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce"> <a href="#projects-section" class="text-primary-600 hover:text-primary-800 transition-colors duration-300"> <i class="fas fa-chevron-down text-2xl"></i> </a> </div> </section>  <section id="projects-section" class="py-20 bg-gray-50 relative overflow-hidden"> <!-- 背景装饰 --> <div class="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(37,117,252,0.1),transparent_50%),radial-gradient(circle_at_80%_20%,rgba(63,218,216,0.1),transparent_50%)]"></div> <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary-400/30 to-transparent"></div> <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10"> <div class="section-header text-center"> <h2 class="text-4xl font-bold text-primary mb-4">我们的项目</h2> <p class="text-gray-600 max-w-2xl mx-auto">
探索我们精心开发的原创软件和工具，每一个项目都承载着我们对技术的热情和对用户体验的追求。
</p> </div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> <div class="unified-card card-3d group cursor-pointer" data-modal-description="一款功能强大的安卓手机工具集合，支持多种系统功能解锁与优化"> <div class="card-header"> <div class="flex items-center justify-between"> <h3 class="text-xl font-bold text-gray-900 group-hover:text-primary transition-colors duration-300">  </h3> <span class="text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded-full"> project1 </span> </div> </div> <div class="card-body"> <div class="relative overflow-hidden rounded-lg mb-4 tech-border"> <img class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"> <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div> <div class="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"> <i class="fas fa-search-plus text-white text-lg"></i> </div> </div> <p class="text-gray-600 text-sm line-clamp-3 group-hover:text-gray-800 transition-colors duration-300"> 一款功能强大的安卓手机工具集合，支持多种系统功能解锁与优化 </p> </div> <div class="card-footer"> <div class="flex items-center justify-between"> <div class="flex items-center space-x-2"> <span class="text-xs text-gray-500"> <i class="fas fa-calendar-alt mr-1"></i>  </span> </div> <div class="flex space-x-2">  </div> </div> </div> </div><div class="unified-card card-3d group cursor-pointer" data-modal-description="即将集成在HOUT工具箱中,敬请期待！"> <div class="card-header"> <div class="flex items-center justify-between"> <h3 class="text-xl font-bold text-gray-900 group-hover:text-primary transition-colors duration-300">  </h3> <span class="text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded-full"> project1 </span> </div> </div> <div class="card-body"> <div class="relative overflow-hidden rounded-lg mb-4 tech-border"> <img class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"> <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div> <div class="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"> <i class="fas fa-search-plus text-white text-lg"></i> </div> </div> <p class="text-gray-600 text-sm line-clamp-3 group-hover:text-gray-800 transition-colors duration-300"> 即将集成在HOUT工具箱中,敬请期待！ </p> </div> <div class="card-footer"> <div class="flex items-center justify-between"> <div class="flex items-center space-x-2"> <span class="text-xs text-gray-500"> <i class="fas fa-calendar-alt mr-1"></i>  </span> </div> <div class="flex space-x-2">  </div> </div> </div> </div><div class="unified-card card-3d group cursor-pointer" data-modal-description="自定义安卓状态栏，支持多种样式和布局调整，让你的状态栏焕然一新"> <div class="card-header"> <div class="flex items-center justify-between"> <h3 class="text-xl font-bold text-gray-900 group-hover:text-primary transition-colors duration-300">  </h3> <span class="text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded-full"> project2 </span> </div> </div> <div class="card-body"> <div class="relative overflow-hidden rounded-lg mb-4 tech-border"> <img class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"> <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div> <div class="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"> <i class="fas fa-search-plus text-white text-lg"></i> </div> </div> <p class="text-gray-600 text-sm line-clamp-3 group-hover:text-gray-800 transition-colors duration-300"> 自定义安卓状态栏，支持多种样式和布局调整，让你的状态栏焕然一新 </p> </div> <div class="card-footer"> <div class="flex items-center justify-between"> <div class="flex items-center space-x-2"> <span class="text-xs text-gray-500"> <i class="fas fa-calendar-alt mr-1"></i>  </span> </div> <div class="flex space-x-2">  </div> </div> </div> </div><div class="unified-card card-3d group cursor-pointer" data-modal-description="自定义安卓状态栏，支持多种样式和布局调整，让你的状态栏焕然一新"> <div class="card-header"> <div class="flex items-center justify-between"> <h3 class="text-xl font-bold text-gray-900 group-hover:text-primary transition-colors duration-300">  </h3> <span class="text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded-full"> project2 </span> </div> </div> <div class="card-body"> <div class="relative overflow-hidden rounded-lg mb-4 tech-border"> <img class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"> <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div> <div class="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"> <i class="fas fa-search-plus text-white text-lg"></i> </div> </div> <p class="text-gray-600 text-sm line-clamp-3 group-hover:text-gray-800 transition-colors duration-300"> 自定义安卓状态栏，支持多种样式和布局调整，让你的状态栏焕然一新 </p> </div> <div class="card-footer"> <div class="flex items-center justify-between"> <div class="flex items-center space-x-2"> <span class="text-xs text-gray-500"> <i class="fas fa-calendar-alt mr-1"></i>  </span> </div> <div class="flex space-x-2">  </div> </div> </div> </div><div class="unified-card card-3d group cursor-pointer" data-modal-description="简单易用的单位转换工具，支持长度、重量、温度等多种单位转换"> <div class="card-header"> <div class="flex items-center justify-between"> <h3 class="text-xl font-bold text-gray-900 group-hover:text-primary transition-colors duration-300">  </h3> <span class="text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded-full"> project3 </span> </div> </div> <div class="card-body"> <div class="relative overflow-hidden rounded-lg mb-4 tech-border"> <img class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"> <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div> <div class="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"> <i class="fas fa-search-plus text-white text-lg"></i> </div> </div> <p class="text-gray-600 text-sm line-clamp-3 group-hover:text-gray-800 transition-colors duration-300"> 简单易用的单位转换工具，支持长度、重量、温度等多种单位转换 </p> </div> <div class="card-footer"> <div class="flex items-center justify-between"> <div class="flex items-center space-x-2"> <span class="text-xs text-gray-500"> <i class="fas fa-calendar-alt mr-1"></i>  </span> </div> <div class="flex space-x-2">  </div> </div> </div> </div> </div> </div> </section>  <section id="sites-section" class="py-20 bg-white relative overflow-hidden"> <!-- 背景装饰 --> <div class="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(37,117,252,0.08),transparent_50%),radial-gradient(circle_at_20%_80%,rgba(63,218,216,0.08),transparent_50%)]"></div> <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-secondary-400/30 to-transparent"></div> <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10"> <div class="section-header text-center"> <h2 class="text-4xl font-bold text-secondary mb-4">其他分站</h2> <p class="text-gray-600 max-w-2xl mx-auto">
发现我们的专业分站，每个分站都专注于特定领域，为您提供更专业、更深入的服务和内容。
</p> </div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> <div class="unified-card card-3d group"> <div class="card-header"> <div class="flex items-center justify-between"> <h3 class="text-xl font-bold text-gray-900 group-hover:text-secondary transition-colors duration-300"> 领创博客 </h3> <span class="text-xs bg-secondary-100 text-secondary-700 px-2 py-1 rounded-full"> blog </span> </div> </div> <div class="card-body"> <div class="relative overflow-hidden rounded-lg mb-4 tech-border"> <img alt="领创博客" class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"> <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div> <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"> <span class="bg-secondary/80 text-white text-xs px-2 py-1 rounded-full"> <i class="fas fa-external-link-alt mr-1"></i>
访问
</span> </div> </div> <p class="text-gray-600 text-sm line-clamp-3 group-hover:text-gray-800 transition-colors duration-300"> 分享数码科技、编程开发、设计创意等方面的文章和教程 </p> </div> <div class="card-footer"> <div class="flex items-center justify-between"> <div class="flex items-center space-x-2"> <span class="text-xs text-gray-500"> <i class="fas fa-globe mr-1"></i>
在线服务
</span> </div> <a target="_blank" rel="noopener noreferrer" class="btn-secondary text-sm px-4 py-2" data-umami-event="访问分站-领创博客"> <i class="fas fa-arrow-right mr-1"></i>
访问分站
</a> </div> </div> </div><div class="unified-card card-3d group"> <div class="card-header"> <div class="flex items-center justify-between"> <h3 class="text-xl font-bold text-gray-900 group-hover:text-secondary transition-colors duration-300"> 资源商店 </h3> <span class="text-xs bg-secondary-100 text-secondary-700 px-2 py-1 rounded-full">  </span> </div> </div> <div class="card-body"> <div class="relative overflow-hidden rounded-lg mb-4 tech-border"> <img alt="资源商店" class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"> <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div> <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"> <span class="bg-secondary/80 text-white text-xs px-2 py-1 rounded-full"> <i class="fas fa-external-link-alt mr-1"></i>
访问
</span> </div> </div> <p class="text-gray-600 text-sm line-clamp-3 group-hover:text-gray-800 transition-colors duration-300"> 提供各种高质量的数码资源下载，包括主题、壁纸、字体等 </p> </div> <div class="card-footer"> <div class="flex items-center justify-between"> <div class="flex items-center space-x-2"> <span class="text-xs text-gray-500"> <i class="fas fa-globe mr-1"></i>
在线服务
</span> </div> <a target="_blank" rel="noopener noreferrer" class="btn-secondary text-sm px-4 py-2" data-umami-event="访问分站-资源商店"> <i class="fas fa-arrow-right mr-1"></i>
访问分站
</a> </div> </div> </div><div class="unified-card card-3d group"> <div class="card-header"> <div class="flex items-center justify-between"> <h3 class="text-xl font-bold text-gray-900 group-hover:text-secondary transition-colors duration-300"> 技术论坛 </h3> <span class="text-xs bg-secondary-100 text-secondary-700 px-2 py-1 rounded-full">  </span> </div> </div> <div class="card-body"> <div class="relative overflow-hidden rounded-lg mb-4 tech-border"> <img alt="技术论坛" class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"> <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div> <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"> <span class="bg-secondary/80 text-white text-xs px-2 py-1 rounded-full"> <i class="fas fa-external-link-alt mr-1"></i>
访问
</span> </div> </div> <p class="text-gray-600 text-sm line-clamp-3 group-hover:text-gray-800 transition-colors duration-300"> 技术交流社区，讨论数码玩机、编程开发、设计创意等话题 </p> </div> <div class="card-footer"> <div class="flex items-center justify-between"> <div class="flex items-center space-x-2"> <span class="text-xs text-gray-500"> <i class="fas fa-globe mr-1"></i>
在线服务
</span> </div> <a target="_blank" rel="noopener noreferrer" class="btn-secondary text-sm px-4 py-2" data-umami-event="访问分站-技术论坛"> <i class="fas fa-arrow-right mr-1"></i>
访问分站
</a> </div> </div> </div><div class="unified-card card-3d group"> <div class="card-header"> <div class="flex items-center justify-between"> <h3 class="text-xl font-bold text-gray-900 group-hover:text-secondary transition-colors duration-300"> 技术支持 </h3> <span class="text-xs bg-secondary-100 text-secondary-700 px-2 py-1 rounded-full">  </span> </div> </div> <div class="card-body"> <div class="relative overflow-hidden rounded-lg mb-4 tech-border"> <img alt="技术支持" class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"> <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div> <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"> <span class="bg-secondary/80 text-white text-xs px-2 py-1 rounded-full"> <i class="fas fa-external-link-alt mr-1"></i>
访问
</span> </div> </div> <p class="text-gray-600 text-sm line-clamp-3 group-hover:text-gray-800 transition-colors duration-300"> 提供项目技术支持、问题解答和个性化定制服务 </p> </div> <div class="card-footer"> <div class="flex items-center justify-between"> <div class="flex items-center space-x-2"> <span class="text-xs text-gray-500"> <i class="fas fa-globe mr-1"></i>
在线服务
</span> </div> <a target="_blank" rel="noopener noreferrer" class="btn-secondary text-sm px-4 py-2" data-umami-event="访问分站-技术支持"> <i class="fas fa-arrow-right mr-1"></i>
访问分站
</a> </div> </div> </div> </div> </div> </section>  <section id="about" class="py-20 bg-gray-50 relative overflow-hidden"> <!-- 背景装饰 --> <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_40%,rgba(37,117,252,0.1),transparent_50%),radial-gradient(circle_at_70%_60%,rgba(63,218,216,0.1),transparent_50%)]"></div> <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-accent-400/30 to-transparent"></div> <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10"> <div class="section-header text-center"> <h2 class="text-4xl font-bold text-accent mb-4">关于我们</h2> <p class="text-gray-600 max-w-2xl mx-auto">
了解领创工作室的故事，我们的使命和愿景，以及我们如何通过技术创新为用户创造价值。
</p> </div> <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"> <div class="space-y-6"> <div class="glass-card p-8"> <div class="flex items-center mb-4"> <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4"> <i class="fas fa-lightbulb text-primary-600 text-xl"></i> </div> <h3 class="text-2xl font-bold text-gray-900">我们的使命</h3> </div> <p class="text-gray-600 leading-relaxed">
致力于开发高质量的原创软件和工具，分享数码科技知识，为用户带来更好的数字体验。我们相信技术应该让生活更美好，让工作更高效。
</p> </div> <div class="glass-card p-8"> <div class="flex items-center mb-4"> <div class="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center mr-4"> <i class="fas fa-rocket text-secondary-600 text-xl"></i> </div> <h3 class="text-2xl font-bold text-gray-900">我们的愿景</h3> </div> <p class="text-gray-600 leading-relaxed">
成为数码科技领域的创新引领者，通过持续的技术创新和优质的产品服务，为全球用户提供卓越的数字化解决方案。
</p> </div> <div class="glass-card p-8"> <div class="flex items-center mb-4"> <div class="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center mr-4"> <i class="fas fa-users text-accent-600 text-xl"></i> </div> <h3 class="text-2xl font-bold text-gray-900">我们的团队</h3> </div> <p class="text-gray-600 leading-relaxed">
由一群热爱技术、富有创造力的开发者组成。我们来自不同的背景，但都怀着同样的热情：用技术改变世界，用创新服务用户。
</p> </div> </div> <div class="relative"> <div class="relative overflow-hidden rounded-xl shadow-neon-accent"> <img src="/img/bg-lacs-group.webp" alt="领创工作室团队" class="w-full h-auto object-cover transform transition-all duration-700 hover:scale-105"> <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div> </div> <!-- 统计数据 --> <div class="grid grid-cols-2 gap-4 mt-8"> <div class="glass-card p-6 text-center"> <div class="text-3xl font-bold text-primary mb-2">10+</div> <div class="text-gray-600 text-sm">原创项目</div> </div> <div class="glass-card p-6 text-center"> <div class="text-3xl font-bold text-secondary mb-2">5+</div> <div class="text-gray-600 text-sm">专业分站</div> </div> <div class="glass-card p-6 text-center"> <div class="text-3xl font-bold text-accent mb-2">1000+</div> <div class="text-gray-600 text-sm">用户信赖</div> </div> <div class="glass-card p-6 text-center"> <div class="text-3xl font-bold text-gray-700 mb-2">24/7</div> <div class="text-gray-600 text-sm">技术支持</div> </div> </div> </div> </div> </div> </section>  <section id="contact" class="py-20 bg-white relative overflow-hidden"> <!-- 背景装饰 --> <div class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(37,117,252,0.08),transparent_50%)]"></div> <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary-400/30 to-transparent"></div> <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10"> <div class="section-header text-center"> <h2 class="text-4xl font-bold text-primary mb-4">联系我们</h2> <p class="text-gray-600 max-w-2xl mx-auto">
有任何问题或建议？我们很乐意听到您的声音。通过以下方式与我们取得联系。
</p> </div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> <!-- 媒体平台 --> <div class="glass-card p-6 text-center group hover:shadow-neon-primary transition-all duration-300"> <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors duration-300"> <i class="undefined text-primary-600 text-2xl"></i> </div> <h3 class="text-xl font-bold text-gray-900 mb-2">哔哩哔哩</h3> <p class="text-gray-600 text-sm mb-4"></p> <a target="_blank" rel="noopener noreferrer" class="btn-primary text-sm px-4 py-2" data-umami-event="访问-哔哩哔哩"> <i class="fas fa-external-link-alt mr-1"></i>
访问平台
</a> </div><div class="glass-card p-6 text-center group hover:shadow-neon-primary transition-all duration-300"> <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors duration-300"> <i class="undefined text-primary-600 text-2xl"></i> </div> <h3 class="text-xl font-bold text-gray-900 mb-2">微信公众号</h3> <p class="text-gray-600 text-sm mb-4"></p> <a target="_blank" rel="noopener noreferrer" class="btn-primary text-sm px-4 py-2" data-umami-event="访问-微信公众号"> <i class="fas fa-external-link-alt mr-1"></i>
访问平台
</a> </div><div class="glass-card p-6 text-center group hover:shadow-neon-primary transition-all duration-300"> <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors duration-300"> <i class="undefined text-primary-600 text-2xl"></i> </div> <h3 class="text-xl font-bold text-gray-900 mb-2">酷安</h3> <p class="text-gray-600 text-sm mb-4"></p> <a target="_blank" rel="noopener noreferrer" class="btn-primary text-sm px-4 py-2" data-umami-event="访问-酷安"> <i class="fas fa-external-link-alt mr-1"></i>
访问平台
</a> </div><div class="glass-card p-6 text-center group hover:shadow-neon-primary transition-all duration-300"> <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors duration-300"> <i class="undefined text-primary-600 text-2xl"></i> </div> <h3 class="text-xl font-bold text-gray-900 mb-2">CSDN</h3> <p class="text-gray-600 text-sm mb-4"></p> <a target="_blank" rel="noopener noreferrer" class="btn-primary text-sm px-4 py-2" data-umami-event="访问-CSDN"> <i class="fas fa-external-link-alt mr-1"></i>
访问平台
</a> </div><div class="glass-card p-6 text-center group hover:shadow-neon-primary transition-all duration-300"> <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors duration-300"> <i class="undefined text-primary-600 text-2xl"></i> </div> <h3 class="text-xl font-bold text-gray-900 mb-2">MT论坛</h3> <p class="text-gray-600 text-sm mb-4"></p> <a target="_blank" rel="noopener noreferrer" class="btn-primary text-sm px-4 py-2" data-umami-event="访问-MT论坛"> <i class="fas fa-external-link-alt mr-1"></i>
访问平台
</a> </div><div class="glass-card p-6 text-center group hover:shadow-neon-primary transition-all duration-300"> <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors duration-300"> <i class="undefined text-primary-600 text-2xl"></i> </div> <h3 class="text-xl font-bold text-gray-900 mb-2">小米社区</h3> <p class="text-gray-600 text-sm mb-4"></p> <a target="_blank" rel="noopener noreferrer" class="btn-primary text-sm px-4 py-2" data-umami-event="访问-小米社区"> <i class="fas fa-external-link-alt mr-1"></i>
访问平台
</a> </div> <!-- 联系信息 --> <div class="glass-card p-6 text-center group hover:shadow-neon-secondary transition-all duration-300"> <div class="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-secondary-200 transition-colors duration-300"> <i class="undefined text-secondary-600 text-2xl"></i> </div> <h3 class="text-xl font-bold text-gray-900 mb-2"></h3> <p class="text-gray-600 text-sm mb-4">24小时内回复</p> <a class="btn-secondary text-sm px-4 py-2" data-umami-event="联系-undefined"> <i class="fas fa-envelope mr-1"></i>
立即联系
</a> </div><div class="glass-card p-6 text-center group hover:shadow-neon-secondary transition-all duration-300"> <div class="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-secondary-200 transition-colors duration-300"> <i class="undefined text-secondary-600 text-2xl"></i> </div> <h3 class="text-xl font-bold text-gray-900 mb-2"></h3> <p class="text-gray-600 text-sm mb-4">商务合作/远程刷机</p> <a class="btn-secondary text-sm px-4 py-2" data-umami-event="联系-undefined"> <i class="fas fa-envelope mr-1"></i>
立即联系
</a> </div><div class="glass-card p-6 text-center group hover:shadow-neon-secondary transition-all duration-300"> <div class="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-secondary-200 transition-colors duration-300"> <i class="undefined text-secondary-600 text-2xl"></i> </div> <h3 class="text-xl font-bold text-gray-900 mb-2"></h3> <p class="text-gray-600 text-sm mb-4">商务合作</p> <a class="btn-secondary text-sm px-4 py-2" data-umami-event="联系-undefined"> <i class="fas fa-envelope mr-1"></i>
立即联系
</a> </div> <!-- 群聊信息 --> <div class="glass-card p-6 text-center group hover:shadow-neon-accent transition-all duration-300"> <div class="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-accent-200 transition-colors duration-300"> <i class="undefined text-accent-600 text-2xl"></i> </div> <h3 class="text-xl font-bold text-gray-900 mb-2">小米玩机交流总群</h3> <p class="text-gray-600 text-sm mb-4"></p> <button class="btn-primary text-sm px-4 py-2" data-modal-title="小米玩机交流总群" data-modal-image="https://img.lacs.cc/img/25-07/06/qqqun-xmwjzq.webp" data-modal-description="扫描二维码加入小米玩机交流总群群聊"> <i class="fas fa-qrcode mr-1"></i>
查看二维码
</button> </div><div class="glass-card p-6 text-center group hover:shadow-neon-accent transition-all duration-300"> <div class="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-accent-200 transition-colors duration-300"> <i class="undefined text-accent-600 text-2xl"></i> </div> <h3 class="text-xl font-bold text-gray-900 mb-2">小米玩机交流群-分群</h3> <p class="text-gray-600 text-sm mb-4"></p> <button class="btn-primary text-sm px-4 py-2" data-modal-title="小米玩机交流群-分群" data-modal-image="https://img.lacs.cc/img/25-07/06/qqun-xmwj2.webp" data-modal-description="扫描二维码加入小米玩机交流群-分群群聊"> <i class="fas fa-qrcode mr-1"></i>
查看二维码
</button> </div><div class="glass-card p-6 text-center group hover:shadow-neon-accent transition-all duration-300"> <div class="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-accent-200 transition-colors duration-300"> <i class="undefined text-accent-600 text-2xl"></i> </div> <h3 class="text-xl font-bold text-gray-900 mb-2">澎湃解锁工具箱(hout)交流总群</h3> <p class="text-gray-600 text-sm mb-4"></p> <button class="btn-primary text-sm px-4 py-2" data-modal-title="澎湃解锁工具箱(hout)交流总群" data-modal-image="https://img.lacs.cc/img/25-07/06/qqun-hout.webp" data-modal-description="扫描二维码加入澎湃解锁工具箱(hout)交流总群群聊"> <i class="fas fa-qrcode mr-1"></i>
查看二维码
</button> </div><div class="glass-card p-6 text-center group hover:shadow-neon-accent transition-all duration-300"> <div class="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-accent-200 transition-colors duration-300"> <i class="undefined text-accent-600 text-2xl"></i> </div> <h3 class="text-xl font-bold text-gray-900 mb-2">QQ频道</h3> <p class="text-gray-600 text-sm mb-4"></p> <button class="btn-primary text-sm px-4 py-2" data-modal-title="QQ频道" data-modal-image="https://img.lacs.cc/img/25-07/06/qqpd.webp" data-modal-description="扫描二维码加入QQ频道群聊"> <i class="fas fa-qrcode mr-1"></i>
查看二维码
</button> </div> </div> </div> </section>  <!-- 页脚 --><footer class="relative overflow-hidden bg-gray-900 text-white py-20"> <!-- 背景装饰 --> <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(37,117,252,0.15),transparent_50%),radial-gradient(circle_at_70%_60%,rgba(63,218,216,0.15),transparent_50%)]"></div> <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-blue-500 via-cyan-400 to-teal-400"></div> <!-- 装饰元素 --> <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-gray-700 to-transparent opacity-30"></div> <div class="absolute -top-20 left-1/4 w-40 h-40 bg-blue-500/5 rounded-full blur-3xl"></div> <div class="absolute -bottom-20 right-1/4 w-40 h-40 bg-teal-500/5 rounded-full blur-3xl"></div> <!-- 内容区域 --> <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10"> <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12"> <!-- 联系我们 - 基础信息板块 --> <div class="bg-gray-800/30 backdrop-blur-md p-6 rounded-xl border border-gray-700/50 shadow-lg hover:shadow-blue-500/20 transition-all duration-500"> <h4 class="text-xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-teal-400 bg-clip-text text-transparent">基础信息</h4> <ul class="space-y-4"> <!-- ICP备案信息 --> <li class="flex items-center gap-3"> <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-blue-500/20 border border-blue-500/30"> <i class="fas fa-file-alt text-blue-400"></i> </div> <div> <a href="https://beian.miit.gov.cn" target="_blank" rel="noopener noreferrer" class="hover:text-blue-400 transition-colors duration-300 flex items-center gap-1"> <span class="text-sm text-gray-300">ICP备案号:</span> <span class="font-medium">辽ICP备2025056705号</span> </a> </div> </li> <!-- 公安备案信息 --> <li class="flex items-center gap-3"> <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-teal-500/20 border border-teal-500/30"> <i class="fas fa-shield-alt text-teal-400"></i> </div> <div> <a href="https://beian.mps.gov.cn/#/query/webSearch?code=21122402000208" rel="noopener noreferrer" target="_blank" class="hover:text-teal-400 transition-colors duration-300 flex items-center gap-1"> <span class="text-sm text-gray-300">公安网备号:</span> <span class="font-medium">辽公网安备21122402000208号</span> </a> </div> </li> <!-- 网站地图 --> <li class="flex items-center gap-3"> <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-purple-500/20 border border-purple-500/30"> <i class="fas fa-sitemap text-purple-400"></i> </div> <div> <a href="/sitemap/sitemap.xml" target="_blank" rel="noopener noreferrer" class="hover:text-purple-400 transition-colors duration-300 flex items-center gap-1"> <span class="text-sm text-gray-300">站点地图:</span> <span class="font-medium">XML格式地图</span> </a> </div> </li> </ul> </div> <!-- 快速链接 --> <div class="bg-gray-800/30 backdrop-blur-md p-6 rounded-xl border border-gray-700/50 shadow-lg hover:shadow-purple-500/20 transition-all duration-500"> <h4 class="text-xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">快速链接</h4> <ul class="space-y-3"> <li> <a href="#projects-section" class="flex items-center justify-between text-gray-300 hover:text-purple-400 transition-all duration-300 group"> <span>项目列表</span> <i class="fas fa-arrow-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i> </a> </li> <li> <a href="#sites-section" class="flex items-center justify-between text-gray-300 hover:text-purple-400 transition-all duration-300 group"> <span>其他分站</span> <i class="fas fa-arrow-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i> </a> </li> <li> <a href="#about-section" class="flex items-center justify-between text-gray-300 hover:text-purple-400 transition-all duration-300 group"> <span>关于我们</span> <i class="fas fa-arrow-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i> </a> </li> </ul> </div> <div class="bg-gray-800/30 backdrop-blur-md p-6 rounded-xl border border-gray-700/50 shadow-lg hover:shadow-cyan-500/20 transition-all duration-500"> <h4 class="text-xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">友情链接</h4> <!-- 新增申请友情链接按钮 --> <button id="apply-link-btn" class="tech-button block bg-gray-700 hover:bg-gray-600 transition-all duration-300 mb-3"> <i class="fas fa-link mr-1"></i> 申请友情链接
</button> <a href="https://jilin9527.top/" class="tech-button block" target="_blank" rel="noopener noreferrer"> <i class="fas fa-external-link-alt mr-1"></i> JiLin的小窝
</a> </div> </div> <!-- 底部版权信息 --> <div class="pt-8 border-t border-gray-800/50"> <div class="flex flex-col md:flex-row justify-between items-center"> <p class="text-gray-400 text-sm mb-4 md:mb-0">
本网站由 <span class="font-medium text-gray-300">领创工作室</span> 提供技术支持
</p> <div class="flex space-x-6"> <p class="mt-6 text-gray-400 text-sm">
&copy; 2020-2025 <span class="bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent font-medium">领创工作室</span>. 保留所有权利.
</p> </div> </div> </div> </div> </footer> <!-- 固定悬浮按钮区域 --><div class="fixed bottom-6 left-1/2 transform -translate-x-1/2 flex flex-row space-x-3 z-40" id="floating-buttons"> <a href="/jz" target="_blank" rel="noopener noreferrer" class="floating-button bg-primary/90 backdrop-blur-sm text-white p-3 rounded-full shadow-neon-primary flex items-center justify-center hover:bg-primary transition-all duration-300 group" title="捐赠我们"> <span class="relative flex items-center justify-center"> <span class="absolute inset-0 rounded-full bg-white/20 blur-sm group-hover:blur-md transition-all duration-300"></span> <i class="fas fa-gift relative z-10"></i> <span class="ml-2 max-w-0 overflow-hidden group-hover:max-w-xs transition-all duration-500 ease-in-out whitespace-nowrap relative z-10">捐赠我们</span> </span> </a> <a href="/yc" class="floating-button bg-secondary/90 backdrop-blur-sm text-white p-3 rounded-full shadow-neon-secondary flex items-center justify-center hover:bg-secondary transition-all duration-300 group" data-umami-event="访问远程刷机" title="远程刷机"> <span class="relative flex items-center justify-center"> <span class="absolute inset-0 rounded-full bg-white/20 blur-sm group-hover:blur-md transition-all duration-300"></span> <i class="fas fa-mobile-alt relative z-10"></i> <span class="ml-2 max-w-0 overflow-hidden group-hover:max-w-xs transition-all duration-500 ease-in-out whitespace-nowrap relative z-10">远程刷机</span> </span> </a> <button id="back-to-top" class="floating-button bg-gray-700/90 backdrop-blur-sm text-white p-3 rounded-full shadow-lg flex items-center justify-center hover:bg-gray-800 hover:shadow-neon-accent transition-all duration-300 group" title="返回顶部"> <span class="relative flex items-center justify-center"> <span class="absolute inset-0 rounded-full bg-white/10 blur-sm group-hover:blur-md transition-all duration-300"></span> <i class="fas fa-arrow-up relative z-10 group-hover:animate-bounce-slow"></i> </span> </button> </div> <!-- 模态框 --><div id="modal" class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 hidden"> <div class="bg-white/95 dark:bg-gray-800/95 rounded-xl border border-gray-200 dark:border-gray-700 shadow-neon-accent dark:shadow-neon-primary tech-border backdrop-blur-md max-w-lg w-full mx-4 transform transition-all duration-300 scale-95 opacity-0" id="modal-content"> <!-- 装饰性网格背景 --> <div class="absolute inset-0 overflow-hidden rounded-xl pointer-events-none"> <div class="absolute inset-0 bg-grid-pattern opacity-10"></div> </div> <div class="p-6 relative z-10"> <div class="flex justify-between items-center mb-6 border-b border-gray-200 dark:border-gray-700 pb-3"> <div class="flex items-center"> <span class="inline-block w-3 h-3 bg-primary rounded-full mr-2 animate-pulse-slow"></span> <h3 class="text-xl font-bold gradient-text" id="modal-title">详情</h3> </div> <button id="close-modal" class="text-gray-500 hover:text-gray-700 hover:rotate-90 transition-all duration-300 w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"> <i class="fas fa-times text-xl"></i> </button> </div> <div class="mb-6" id="modal-body"> <div class="relative overflow-hidden rounded-lg mb-4 tech-border"> <img id="modal-image" src="" alt="" class="max-w-full h-auto rounded-lg transition-transform duration-500 hover:scale-105"> <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div> </div> <p id="modal-description" class="mt-4 text-gray-600 dark:text-gray-300 leading-relaxed"></p> </div> </div> </div> </div> <!-- 友情链接申请模态框 --> <div id="link-modal" class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 hidden"> <div class="bg-white/95 dark:bg-gray-800/95 rounded-xl border border-gray-200 dark:border-gray-700 shadow-neon-accent dark:shadow-neon-primary tech-border backdrop-blur-md max-w-lg w-full mx-4 transform transition-all duration-300 scale-95 opacity-0" id="link-modal-content"> <div class="absolute inset-0 overflow-hidden rounded-xl pointer-events-none"> <div class="absolute inset-0 bg-grid-pattern opacity-10"></div> </div> <div class="p-6 relative z-10"> <div class="flex justify-between items-center mb-6 border-b border-gray-200 dark:border-gray-700 pb-3"> <div class="flex items-center"> <span class="inline-block w-3 h-3 bg-primary rounded-full mr-2 animate-pulse-slow"></span> <h3 class="text-xl font-bold gradient-text">申请友情链接</h3> </div> <button id="close-link-modal" class="text-gray-500 hover:text-gray-700 hover:rotate-90 transition-all duration-300 w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"> <i class="fas fa-times text-xl"></i> </button> </div> <div class="mb-6"> <p class="text-gray-600 dark:text-gray-300 mb-4">请将以下网站基本信息发送至：<strong><EMAIL></strong></p> <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-2"> <li>网站名称</li> <li>网站URL地址</li> <li>网站简介</li> <li>网站Logo（200x200px）</li> <li>您的联系方式</li> </ul> <div class="mt-6 p-4 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800"> <p class="text-sm text-gray-600 dark:text-gray-300"><i class="fas fa-info-circle text-primary mr-2"></i> 我们会在3-5个工作日内审核并回复您的申请</p> </div> </div> </div> </div> </div>  <script src="/js/main.js"></script>  <!-- Umami Analytics --> <script defer src="https://umami.lacs.cc/script.js" data-website-id="a4e8c20f-d2e8-4b10-bdf5-2d52c389fd45"></script> </body> </html>