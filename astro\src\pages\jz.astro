---
import Layout from '../layouts/Layout.astro';
import Navigation from '../components/Navigation.astro';
import Footer from '../components/Footer.astro';
---

<Layout title="捐赠我们 - 领创工作室">
  <Navigation />
  
  <!-- 捐赠页面内容 -->
  <section class="pt-28 pb-16 min-h-screen bg-gray-50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="max-w-4xl mx-auto">
        <!-- 页面标题 -->
        <div class="text-center mb-12">
          <h1 class="text-4xl font-bold gradient-text mb-4">支持我们的发展</h1>
          <p class="text-gray-600 text-lg max-w-2xl mx-auto">
            您的每一份支持都是我们前进的动力，帮助我们创造更好的产品和服务
          </p>
        </div>

        <!-- 捐赠说明 -->
        <div class="glass-card p-8 mb-8">
          <div class="flex items-center mb-6">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
              <i class="fas fa-heart text-primary-600 text-xl"></i>
            </div>
            <h2 class="text-2xl font-bold text-gray-900">为什么需要您的支持？</h2>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div class="flex items-start">
                <i class="fas fa-server text-primary-500 mt-1 mr-3"></i>
                <div>
                  <h3 class="font-semibold text-gray-900 mb-1">服务器维护</h3>
                  <p class="text-gray-600 text-sm">维持网站和服务的稳定运行</p>
                </div>
              </div>
              <div class="flex items-start">
                <i class="fas fa-code text-primary-500 mt-1 mr-3"></i>
                <div>
                  <h3 class="font-semibold text-gray-900 mb-1">软件开发</h3>
                  <p class="text-gray-600 text-sm">投入更多时间开发优质软件</p>
                </div>
              </div>
            </div>
            <div class="space-y-4">
              <div class="flex items-start">
                <i class="fas fa-graduation-cap text-primary-500 mt-1 mr-3"></i>
                <div>
                  <h3 class="font-semibold text-gray-900 mb-1">技术学习</h3>
                  <p class="text-gray-600 text-sm">学习新技术，提升服务质量</p>
                </div>
              </div>
              <div class="flex items-start">
                <i class="fas fa-users text-primary-500 mt-1 mr-3"></i>
                <div>
                  <h3 class="font-semibold text-gray-900 mb-1">团队发展</h3>
                  <p class="text-gray-600 text-sm">扩大团队，提供更好的支持</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 捐赠方式 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- 微信支付 -->
          <div class="glass-card p-8 text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fab fa-weixin text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">微信支付</h3>
            <div class="bg-white p-4 rounded-lg inline-block mb-4">
              <img src="/img/捐赠.svg" alt="微信支付二维码" class="w-48 h-48 mx-auto">
            </div>
            <p class="text-gray-600 text-sm">
              扫描二维码使用微信支付进行捐赠
            </p>
          </div>

          <!-- 支付宝 -->
          <div class="glass-card p-8 text-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fab fa-alipay text-blue-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">支付宝</h3>
            <div class="bg-white p-4 rounded-lg inline-block mb-4">
              <img src="/img/捐赠.svg" alt="支付宝二维码" class="w-48 h-48 mx-auto">
            </div>
            <p class="text-gray-600 text-sm">
              扫描二维码使用支付宝进行捐赠
            </p>
          </div>
        </div>

        <!-- 感谢信息 -->
        <div class="glass-card p-8 mt-8 text-center">
          <div class="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-gift text-accent-600 text-2xl"></i>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-4">感谢您的支持</h3>
          <p class="text-gray-600 leading-relaxed max-w-2xl mx-auto">
            无论捐赠金额大小，我们都深表感谢。您的支持让我们能够继续专注于创造有价值的产品和服务。
            我们承诺将每一份捐赠都用于改善我们的服务质量和用户体验。
          </p>
          
          <div class="mt-8 flex flex-wrap justify-center gap-4">
            <a href="/" class="btn-primary">
              <i class="fas fa-home mr-2"></i>
              返回首页
            </a>
            <a href="#contact" class="btn-secondary">
              <i class="fas fa-envelope mr-2"></i>
              联系我们
            </a>
          </div>
        </div>

        <!-- 其他支持方式 -->
        <div class="glass-card p-8 mt-8">
          <h3 class="text-xl font-bold text-gray-900 mb-6 text-center">其他支持方式</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
              <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-share-alt text-purple-600"></i>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">分享推荐</h4>
              <p class="text-gray-600 text-sm">向朋友推荐我们的产品和服务</p>
            </div>
            <div class="text-center">
              <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-bug text-orange-600"></i>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">反馈建议</h4>
              <p class="text-gray-600 text-sm">提供宝贵的意见和建议</p>
            </div>
            <div class="text-center">
              <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-star text-teal-600"></i>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">评价支持</h4>
              <p class="text-gray-600 text-sm">在各平台给予好评和支持</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <Footer />
  
  <!-- JavaScript -->
  <script src="/js/main.js" is:inline></script>
</Layout>
