// 主要JavaScript功能
document.addEventListener('DOMContentLoaded', function() {
    // 导航栏滚动效果
    const nav = document.getElementById('main-nav');
    let lastScrollY = window.scrollY;
    
    window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY;
        
        if (currentScrollY > 100) {
            nav.classList.remove('nav-default');
            nav.classList.add('nav-scrolled');
        } else {
            nav.classList.remove('nav-scrolled');
            nav.classList.add('nav-default');
        }
        
        lastScrollY = currentScrollY;
    });

    // 移动端菜单切换
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (menuToggle && mobileMenu) {
        menuToggle.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
            const icon = menuToggle.querySelector('i');
            if (mobileMenu.classList.contains('hidden')) {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            } else {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            }
        });
    }

    // 深色模式切换
    const darkModeToggle = document.getElementById('dark-mode-toggle');
    const mobileDarkModeToggle = document.getElementById('mobile-dark-mode-toggle');
    
    function toggleDarkMode() {
        document.documentElement.classList.toggle('dark');
        const isDark = document.documentElement.classList.contains('dark');
        localStorage.setItem('darkMode', isDark);
        
        // 更新图标
        const desktopIcon = darkModeToggle?.querySelector('i');
        const mobileIcon = mobileDarkModeToggle?.querySelector('i');
        
        if (isDark) {
            desktopIcon?.classList.replace('fa-moon', 'fa-sun');
            mobileIcon?.classList.replace('fa-moon', 'fa-sun');
        } else {
            desktopIcon?.classList.replace('fa-sun', 'fa-moon');
            mobileIcon?.classList.replace('fa-sun', 'fa-moon');
        }
    }
    
    // 初始化深色模式
    const savedDarkMode = localStorage.getItem('darkMode');
    if (savedDarkMode === 'true') {
        document.documentElement.classList.add('dark');
        const desktopIcon = darkModeToggle?.querySelector('i');
        const mobileIcon = mobileDarkModeToggle?.querySelector('i');
        desktopIcon?.classList.replace('fa-moon', 'fa-sun');
        mobileIcon?.classList.replace('fa-moon', 'fa-sun');
    }
    
    darkModeToggle?.addEventListener('click', toggleDarkMode);
    mobileDarkModeToggle?.addEventListener('click', toggleDarkMode);

    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // 关闭移动端菜单
                if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                    const icon = menuToggle?.querySelector('i');
                    icon?.classList.remove('fa-times');
                    icon?.classList.add('fa-bars');
                }
            }
        });
    });

    // 返回顶部按钮
    const backToTopBtn = document.getElementById('back-to-top');
    
    if (backToTopBtn) {
        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // 模态框功能
    const modal = document.getElementById('modal');
    const modalContent = document.getElementById('modal-content');
    const modalTitle = document.getElementById('modal-title');
    const modalImage = document.getElementById('modal-image');
    const modalDescription = document.getElementById('modal-description');
    const closeModal = document.getElementById('close-modal');

    // 打开模态框
    function openModal(title, image, description) {
        if (modal && modalContent) {
            modalTitle.textContent = title;
            modalImage.src = image;
            modalImage.alt = title;
            modalDescription.textContent = description;
            
            modal.classList.remove('hidden');
            setTimeout(() => {
                modalContent.classList.remove('scale-95', 'opacity-0');
                modalContent.classList.add('scale-100', 'opacity-100');
            }, 10);
        }
    }

    // 关闭模态框
    function closeModalFunc() {
        if (modal && modalContent) {
            modalContent.classList.remove('scale-100', 'opacity-100');
            modalContent.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                modal.classList.add('hidden');
            }, 300);
        }
    }

    // 绑定模态框事件
    document.querySelectorAll('[data-modal-title]').forEach(element => {
        element.addEventListener('click', () => {
            const title = element.getAttribute('data-modal-title');
            const image = element.getAttribute('data-modal-image');
            const description = element.getAttribute('data-modal-description');
            openModal(title, image, description);
        });
    });

    closeModal?.addEventListener('click', closeModalFunc);
    modal?.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModalFunc();
        }
    });

    // 友情链接申请模态框
    const linkModal = document.getElementById('link-modal');
    const linkModalContent = document.getElementById('link-modal-content');
    const closeLinkModal = document.getElementById('close-link-modal');
    const applyLinkBtn = document.getElementById('apply-link-btn');

    function openLinkModal() {
        if (linkModal && linkModalContent) {
            linkModal.classList.remove('hidden');
            setTimeout(() => {
                linkModalContent.classList.remove('scale-95', 'opacity-0');
                linkModalContent.classList.add('scale-100', 'opacity-100');
            }, 10);
        }
    }

    function closeLinkModalFunc() {
        if (linkModal && linkModalContent) {
            linkModalContent.classList.remove('scale-100', 'opacity-100');
            linkModalContent.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                linkModal.classList.add('hidden');
            }, 300);
        }
    }

    applyLinkBtn?.addEventListener('click', openLinkModal);
    closeLinkModal?.addEventListener('click', closeLinkModalFunc);
    linkModal?.addEventListener('click', (e) => {
        if (e.target === linkModal) {
            closeLinkModalFunc();
        }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            closeModalFunc();
            closeLinkModalFunc();
        }
    });

    // 粒子效果初始化
    function initParticles() {
        if (typeof particlesJS !== 'undefined') {
            particlesJS('particles-js', {
                particles: {
                    number: { value: 80, density: { enable: true, value_area: 800 } },
                    color: { value: '#1C64F2' },
                    shape: { type: 'circle' },
                    opacity: { value: 0.5, random: false },
                    size: { value: 3, random: true },
                    line_linked: { enable: true, distance: 150, color: '#1C64F2', opacity: 0.4, width: 1 },
                    move: { enable: true, speed: 6, direction: 'none', random: false, straight: false, out_mode: 'out', bounce: false }
                },
                interactivity: {
                    detect_on: 'canvas',
                    events: { onhover: { enable: true, mode: 'repulse' }, onclick: { enable: true, mode: 'push' }, resize: true },
                    modes: { grab: { distance: 400, line_linked: { opacity: 1 } }, bubble: { distance: 400, size: 40, duration: 2, opacity: 8, speed: 3 }, repulse: { distance: 200, duration: 0.4 }, push: { particles_nb: 4 }, remove: { particles_nb: 2 } }
                },
                retina_detect: true
            });
        }
    }

    // 延迟初始化粒子效果
    setTimeout(initParticles, 1000);

    // 添加加载动画
    window.addEventListener('load', () => {
        document.body.classList.add('loaded');
    });
});
