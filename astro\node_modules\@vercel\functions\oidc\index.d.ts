/**
 * @deprecated Use @vercel/oidc-aws-credentials-provider instead
 * AWS authentication for Vercel Functions
 */
export type { AwsCredentialsProviderInit } from './aws-credentials-provider';
/**
 * @deprecated Use @vercel/oidc-aws-credentials-provider instead
 * AWS authentication for Vercel Functions
 */
export { awsCredentialsProvider } from './aws-credentials-provider';
/**
 * @deprecated Use @vercel/oidc instead
 * OIDC authentication for Vercel Functions
 */
export { getVercelOidcToken, getVercelOidcTokenSync } from '@vercel/oidc';
