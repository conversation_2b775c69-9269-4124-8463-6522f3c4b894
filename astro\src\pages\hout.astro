---
import Layout from '../layouts/Layout.astro';
import Navigation from '../components/Navigation.astro';
import Footer from '../components/Footer.astro';
---

<Layout title="HOUT工具 - 领创工作室">
  <Navigation />
  
  <!-- HOUT工具页面内容 -->
  <section class="pt-28 pb-16 min-h-screen bg-gray-50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="max-w-4xl mx-auto">
        <!-- 页面标题 -->
        <div class="text-center mb-12">
          <h1 class="text-4xl font-bold gradient-text mb-4">HOUT工具</h1>
          <p class="text-gray-600 text-lg max-w-2xl mx-auto">
            专业的Android设备管理工具，让您的设备管理更加简单高效
          </p>
        </div>

        <!-- 工具介绍 -->
        <div class="glass-card p-8 mb-8">
          <div class="flex items-center mb-6">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
              <i class="fas fa-tools text-primary-600 text-xl"></i>
            </div>
            <h2 class="text-2xl font-bold text-gray-900">关于HOUT工具</h2>
          </div>
          
          <p class="text-gray-600 leading-relaxed mb-6">
            HOUT是我们开发的一款专业Android设备管理工具，集成了多种实用功能，
            帮助用户更好地管理和优化他们的Android设备。
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div class="flex items-start">
                <i class="fas fa-mobile-alt text-green-500 mt-1 mr-3"></i>
                <div>
                  <h3 class="font-semibold text-gray-900 mb-1">设备管理</h3>
                  <p class="text-gray-600 text-sm">全面的设备信息查看和管理</p>
                </div>
              </div>
              <div class="flex items-start">
                <i class="fas fa-download text-blue-500 mt-1 mr-3"></i>
                <div>
                  <h3 class="font-semibold text-gray-900 mb-1">应用管理</h3>
                  <p class="text-gray-600 text-sm">应用安装、卸载和备份</p>
                </div>
              </div>
            </div>
            <div class="space-y-4">
              <div class="flex items-start">
                <i class="fas fa-file-archive text-purple-500 mt-1 mr-3"></i>
                <div>
                  <h3 class="font-semibold text-gray-900 mb-1">文件管理</h3>
                  <p class="text-gray-600 text-sm">设备文件的传输和管理</p>
                </div>
              </div>
              <div class="flex items-start">
                <i class="fas fa-cog text-orange-500 mt-1 mr-3"></i>
                <div>
                  <h3 class="font-semibold text-gray-900 mb-1">系统优化</h3>
                  <p class="text-gray-600 text-sm">系统清理和性能优化</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 功能特色 -->
        <div class="glass-card p-8 mb-8">
          <h3 class="text-xl font-bold text-gray-900 mb-6 text-center">主要功能</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="text-center p-4 border border-gray-200 rounded-lg">
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-info-circle text-blue-600"></i>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">设备信息</h4>
              <p class="text-gray-600 text-sm">查看详细的设备硬件和系统信息</p>
            </div>
            <div class="text-center p-4 border border-gray-200 rounded-lg">
              <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-apps text-green-600"></i>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">应用管理</h4>
              <p class="text-gray-600 text-sm">批量安装、卸载和管理应用</p>
            </div>
            <div class="text-center p-4 border border-gray-200 rounded-lg">
              <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-folder text-purple-600"></i>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">文件传输</h4>
              <p class="text-gray-600 text-sm">快速的文件传输和备份功能</p>
            </div>
            <div class="text-center p-4 border border-gray-200 rounded-lg">
              <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-broom text-red-600"></i>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">系统清理</h4>
              <p class="text-gray-600 text-sm">清理垃圾文件，释放存储空间</p>
            </div>
            <div class="text-center p-4 border border-gray-200 rounded-lg">
              <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-shield-alt text-yellow-600"></i>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">安全检测</h4>
              <p class="text-gray-600 text-sm">检测设备安全状态和风险</p>
            </div>
            <div class="text-center p-4 border border-gray-200 rounded-lg">
              <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-chart-line text-indigo-600"></i>
              </div>
              <h4 class="font-semibold text-gray-900 mb-2">性能监控</h4>
              <p class="text-gray-600 text-sm">实时监控设备性能状态</p>
            </div>
          </div>
        </div>

        <!-- 下载信息 -->
        <div class="glass-card p-8 text-center">
          <h3 class="text-xl font-bold text-gray-900 mb-6">下载HOUT工具</h3>
          <p class="text-gray-600 mb-6">
            目前HOUT工具正在开发中，敬请期待正式版本的发布
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-md mx-auto">
            <button class="btn-primary opacity-50 cursor-not-allowed" disabled>
              <i class="fas fa-download mr-2"></i>
              Windows版本
            </button>
            <button class="btn-secondary opacity-50 cursor-not-allowed" disabled>
              <i class="fas fa-download mr-2"></i>
              Mac版本
            </button>
          </div>
          
          <p class="text-gray-500 text-sm mt-4">
            开发进度：60% | 预计发布时间：2025年第二季度
          </p>
          
          <div class="mt-8">
            <a href="/" class="btn-primary">
              <i class="fas fa-home mr-2"></i>
              返回首页
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <Footer />
  
  <!-- JavaScript -->
  <script src="/js/main.js" is:inline></script>
</Layout>
