import type { Pool as PgPoolType } from 'pg';
import type { Pool as MySQL2PoolType } from 'mysql2/promise';
import type { Pool as MariaDBPoolType } from 'mariadb';
import type { MongoClient as MongoDBClientType } from 'mongodb';
import type { Redis as IoRedisType } from 'ioredis';
import type { Client as CassandraClientType } from 'cassandra-driver';
import type { DbPool } from '.';
export declare const pgSubtypeCheck: (pool: PgPoolType) => DbPool;
export declare const mysql2SubtypeCheck: (pool: MySQL2PoolType) => DbPool;
export declare const mariadbSubtypeCheck: (pool: MariaDBPoolType) => DbPool;
export declare const mongoSubtypeCheck: (client: MongoDBClientType) => DbPool;
export declare const ioredisSubtypeCheck: (redis: IoRedisType) => DbPool;
export declare const cassandraSubtypeCheck: (client: CassandraClientType) => DbPool;
