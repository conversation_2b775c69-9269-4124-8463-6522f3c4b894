<!DOCTYPE html><html lang="zh-CN"> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="description" content="领创工作室专注于数码科技领域，提供原创软件工具、技术教程和创意内容，涵盖编程、设计、摄影、音乐等多个方面。"><meta name="keywords" content="领创工作室,领创,LACS,领创科技,数码科技创新,原创软件工具,编程教程,设计资源,摄影作品,音乐创作"><meta property="og:title" content="HOUT工具 - 领创工作室"><meta property="og:description" content="领创工作室专注于数码科技领域，提供原创软件工具、技术教程和创意内容，涵盖编程、设计、摄影、音乐等多个方面。"><meta property="og:image" content="https://gitee.com/lacsgf/img/raw/master/webp/bg-lacs-group.webp"><meta name="robots" content="index, follow"><link rel="canonical" href="https://lacs.cc/"><!-- 引入Font Awesome --><link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet"><!-- 引入粒子效果库 --><script type="module" src="/assets/Layout.astro_astro_type_script_index_0_lang.BE6OMyjS.js"></script><link rel="icon" href="/img/favicon.ico" type="image/x-icon"><title>HOUT工具 - 领创工作室</title><link rel="stylesheet" href="/assets/hout.CD17msai.css"><script>window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };
		var script = document.createElement('script');
		script.defer = true;
		script.src = '/_vercel/insights/script.js';
		var head = document.querySelector('head');
		head.appendChild(script);
	</script></head> <body class="font-inter bg-gray-50 text-gray-800">  <!-- 导航栏 --><nav class="fixed top-0 left-0 right-0 z-50 transition-all duration-500 nav-default bg-white/80 backdrop-blur-sm p-4" id="main-nav"> <div class="container mx-auto flex justify-between items-center"> <a class="flex items-center group" href="/"> <div class="relative overflow-hidden rounded-full p-0.5 mr-2 bg-gradient-to-r from-primary-400 to-secondary-400 animate-pulse-slow"> <img src="/img/lacs.webp" class="h-8 w-8 rounded-full object-cover transform group-hover:scale-110 transition-transform duration-300" alt="领创工作室Logo"> </div> <span class="text-xl font-bold gradient-text">领创工作室</span> </a> <button class="navbar-toggler md:hidden text-gray-700 focus:outline-none hover:text-gray-900 transition-colors duration-300" type="button" id="menu-toggle" aria-label="切换菜单"> <i class="fas fa-bars text-xl"></i> </button> <div class="hidden md:flex items-center space-x-8" id="navbar-menu"> <a href="/" class="relative text-gray-700 hover:text-gray-900 transition-colors duration-300 group"> <span>首页</span> <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gray-900 group-hover:w-full transition-all duration-300"></span> </a> <a href="#projects-section" class="relative text-gray-700 hover:text-gray-900 transition-colors duration-300 group"> <span>项目</span> <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gray-900 group-hover:w-full transition-all duration-300"></span> </a> <a href="#sites-section" class="relative text-gray-700 hover:text-gray-900 transition-colors duration-300 group"> <span>分站</span> <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gray-900 group-hover:w-full transition-all duration-300"></span> </a> <a href="#about" class="relative text-gray-700 hover:text-gray-900 transition-colors duration-300 group"> <span>关于我们</span> <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gray-900 group-hover:w-full transition-all duration-300"></span> </a> <button class="btn-dark-mode text-gray-700 hover:text-gray-900 transition-all duration-300 transform hover:rotate-12" id="dark-mode-toggle" aria-label="切换深色模式"> <i class="fas fa-moon text-xl"></i> </button> </div> </div> <!-- 移动端菜单 --> <div class="md:hidden bg-white/80 backdrop-blur-sm hidden transition-all duration-500 border-t border-gray-200 p-4" id="mobile-menu"> <div class="container mx-auto flex flex-col space-y-4"> <a href="/" class="text-gray-700 hover:text-gray-900 hover:pl-2 transition-all duration-300 py-2 border-b border-gray-200"> <i class="fas fa-home mr-2 text-primary-400"></i> 首页
</a> <a href="#projects-section" class="text-gray-700 hover:text-gray-900 hover:pl-2 transition-all duration-300 py-2 border-b border-gray-200"> <i class="fas fa-project-diagram mr-2 text-primary-400"></i> 项目
</a> <a href="#sites-section" class="text-gray-700 hover:text-gray-900 hover:pl-2 transition-all duration-300 py-2 border-b border-gray-200"> <i class="fas fa-sitemap mr-2 text-primary-400"></i> 分站
</a> <a href="#about" class="text-gray-700 hover:text-gray-900 hover:pl-2 transition-all duration-300 py-2 border-b border-gray-200"> <i class="fas fa-info-circle mr-2 text-primary-400"></i> 关于我们
</a> <button class="flex items-center text-gray-700 hover:text-gray-900 hover:pl-2 transition-all duration-300 py-2" id="mobile-dark-mode-toggle" aria-label="切换深色模式"> <i class="fas fa-moon mr-2 text-primary-400"></i> 深色模式
</button> </div> </div> </nav>  <section class="pt-28 pb-16 min-h-screen bg-gray-50"> <div class="container mx-auto px-4 sm:px-6 lg:px-8"> <div class="max-w-4xl mx-auto"> <!-- 页面标题 --> <div class="text-center mb-12"> <h1 class="text-4xl font-bold gradient-text mb-4">HOUT工具</h1> <p class="text-gray-600 text-lg max-w-2xl mx-auto">
专业的Android设备管理工具，让您的设备管理更加简单高效
</p> </div> <!-- 工具介绍 --> <div class="glass-card p-8 mb-8"> <div class="flex items-center mb-6"> <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4"> <i class="fas fa-tools text-primary-600 text-xl"></i> </div> <h2 class="text-2xl font-bold text-gray-900">关于HOUT工具</h2> </div> <p class="text-gray-600 leading-relaxed mb-6">
HOUT是我们开发的一款专业Android设备管理工具，集成了多种实用功能，
            帮助用户更好地管理和优化他们的Android设备。
</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <div class="space-y-4"> <div class="flex items-start"> <i class="fas fa-mobile-alt text-green-500 mt-1 mr-3"></i> <div> <h3 class="font-semibold text-gray-900 mb-1">设备管理</h3> <p class="text-gray-600 text-sm">全面的设备信息查看和管理</p> </div> </div> <div class="flex items-start"> <i class="fas fa-download text-blue-500 mt-1 mr-3"></i> <div> <h3 class="font-semibold text-gray-900 mb-1">应用管理</h3> <p class="text-gray-600 text-sm">应用安装、卸载和备份</p> </div> </div> </div> <div class="space-y-4"> <div class="flex items-start"> <i class="fas fa-file-archive text-purple-500 mt-1 mr-3"></i> <div> <h3 class="font-semibold text-gray-900 mb-1">文件管理</h3> <p class="text-gray-600 text-sm">设备文件的传输和管理</p> </div> </div> <div class="flex items-start"> <i class="fas fa-cog text-orange-500 mt-1 mr-3"></i> <div> <h3 class="font-semibold text-gray-900 mb-1">系统优化</h3> <p class="text-gray-600 text-sm">系统清理和性能优化</p> </div> </div> </div> </div> </div> <!-- 功能特色 --> <div class="glass-card p-8 mb-8"> <h3 class="text-xl font-bold text-gray-900 mb-6 text-center">主要功能</h3> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> <div class="text-center p-4 border border-gray-200 rounded-lg"> <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3"> <i class="fas fa-info-circle text-blue-600"></i> </div> <h4 class="font-semibold text-gray-900 mb-2">设备信息</h4> <p class="text-gray-600 text-sm">查看详细的设备硬件和系统信息</p> </div> <div class="text-center p-4 border border-gray-200 rounded-lg"> <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3"> <i class="fas fa-apps text-green-600"></i> </div> <h4 class="font-semibold text-gray-900 mb-2">应用管理</h4> <p class="text-gray-600 text-sm">批量安装、卸载和管理应用</p> </div> <div class="text-center p-4 border border-gray-200 rounded-lg"> <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3"> <i class="fas fa-folder text-purple-600"></i> </div> <h4 class="font-semibold text-gray-900 mb-2">文件传输</h4> <p class="text-gray-600 text-sm">快速的文件传输和备份功能</p> </div> <div class="text-center p-4 border border-gray-200 rounded-lg"> <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-3"> <i class="fas fa-broom text-red-600"></i> </div> <h4 class="font-semibold text-gray-900 mb-2">系统清理</h4> <p class="text-gray-600 text-sm">清理垃圾文件，释放存储空间</p> </div> <div class="text-center p-4 border border-gray-200 rounded-lg"> <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3"> <i class="fas fa-shield-alt text-yellow-600"></i> </div> <h4 class="font-semibold text-gray-900 mb-2">安全检测</h4> <p class="text-gray-600 text-sm">检测设备安全状态和风险</p> </div> <div class="text-center p-4 border border-gray-200 rounded-lg"> <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-3"> <i class="fas fa-chart-line text-indigo-600"></i> </div> <h4 class="font-semibold text-gray-900 mb-2">性能监控</h4> <p class="text-gray-600 text-sm">实时监控设备性能状态</p> </div> </div> </div> <!-- 下载信息 --> <div class="glass-card p-8 text-center"> <h3 class="text-xl font-bold text-gray-900 mb-6">下载HOUT工具</h3> <p class="text-gray-600 mb-6">
目前HOUT工具正在开发中，敬请期待正式版本的发布
</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-md mx-auto"> <button class="btn-primary opacity-50 cursor-not-allowed" disabled> <i class="fas fa-download mr-2"></i>
Windows版本
</button> <button class="btn-secondary opacity-50 cursor-not-allowed" disabled> <i class="fas fa-download mr-2"></i>
Mac版本
</button> </div> <p class="text-gray-500 text-sm mt-4">
开发进度：60% | 预计发布时间：2025年第二季度
</p> <div class="mt-8"> <a href="/" class="btn-primary"> <i class="fas fa-home mr-2"></i>
返回首页
</a> </div> </div> </div> </div> </section> <!-- 页脚 --><footer class="relative overflow-hidden bg-gray-900 text-white py-20"> <!-- 背景装饰 --> <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(37,117,252,0.15),transparent_50%),radial-gradient(circle_at_70%_60%,rgba(63,218,216,0.15),transparent_50%)]"></div> <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-blue-500 via-cyan-400 to-teal-400"></div> <!-- 装饰元素 --> <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-gray-700 to-transparent opacity-30"></div> <div class="absolute -top-20 left-1/4 w-40 h-40 bg-blue-500/5 rounded-full blur-3xl"></div> <div class="absolute -bottom-20 right-1/4 w-40 h-40 bg-teal-500/5 rounded-full blur-3xl"></div> <!-- 内容区域 --> <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10"> <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12"> <!-- 联系我们 - 基础信息板块 --> <div class="bg-gray-800/30 backdrop-blur-md p-6 rounded-xl border border-gray-700/50 shadow-lg hover:shadow-blue-500/20 transition-all duration-500"> <h4 class="text-xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-teal-400 bg-clip-text text-transparent">基础信息</h4> <ul class="space-y-4"> <!-- ICP备案信息 --> <li class="flex items-center gap-3"> <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-blue-500/20 border border-blue-500/30"> <i class="fas fa-file-alt text-blue-400"></i> </div> <div> <a href="https://beian.miit.gov.cn" target="_blank" rel="noopener noreferrer" class="hover:text-blue-400 transition-colors duration-300 flex items-center gap-1"> <span class="text-sm text-gray-300">ICP备案号:</span> <span class="font-medium">辽ICP备2025056705号</span> </a> </div> </li> <!-- 公安备案信息 --> <li class="flex items-center gap-3"> <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-teal-500/20 border border-teal-500/30"> <i class="fas fa-shield-alt text-teal-400"></i> </div> <div> <a href="https://beian.mps.gov.cn/#/query/webSearch?code=21122402000208" rel="noopener noreferrer" target="_blank" class="hover:text-teal-400 transition-colors duration-300 flex items-center gap-1"> <span class="text-sm text-gray-300">公安网备号:</span> <span class="font-medium">辽公网安备21122402000208号</span> </a> </div> </li> <!-- 网站地图 --> <li class="flex items-center gap-3"> <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-purple-500/20 border border-purple-500/30"> <i class="fas fa-sitemap text-purple-400"></i> </div> <div> <a href="/sitemap/sitemap.xml" target="_blank" rel="noopener noreferrer" class="hover:text-purple-400 transition-colors duration-300 flex items-center gap-1"> <span class="text-sm text-gray-300">站点地图:</span> <span class="font-medium">XML格式地图</span> </a> </div> </li> </ul> </div> <!-- 快速链接 --> <div class="bg-gray-800/30 backdrop-blur-md p-6 rounded-xl border border-gray-700/50 shadow-lg hover:shadow-purple-500/20 transition-all duration-500"> <h4 class="text-xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">快速链接</h4> <ul class="space-y-3"> <li> <a href="#projects-section" class="flex items-center justify-between text-gray-300 hover:text-purple-400 transition-all duration-300 group"> <span>项目列表</span> <i class="fas fa-arrow-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i> </a> </li> <li> <a href="#sites-section" class="flex items-center justify-between text-gray-300 hover:text-purple-400 transition-all duration-300 group"> <span>其他分站</span> <i class="fas fa-arrow-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i> </a> </li> <li> <a href="#about-section" class="flex items-center justify-between text-gray-300 hover:text-purple-400 transition-all duration-300 group"> <span>关于我们</span> <i class="fas fa-arrow-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i> </a> </li> </ul> </div> <div class="bg-gray-800/30 backdrop-blur-md p-6 rounded-xl border border-gray-700/50 shadow-lg hover:shadow-cyan-500/20 transition-all duration-500"> <h4 class="text-xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">友情链接</h4> <!-- 新增申请友情链接按钮 --> <button id="apply-link-btn" class="tech-button block bg-gray-700 hover:bg-gray-600 transition-all duration-300 mb-3"> <i class="fas fa-link mr-1"></i> 申请友情链接
</button> <a href="https://jilin9527.top/" class="tech-button block" target="_blank" rel="noopener noreferrer"> <i class="fas fa-external-link-alt mr-1"></i> JiLin的小窝
</a> </div> </div> <!-- 底部版权信息 --> <div class="pt-8 border-t border-gray-800/50"> <div class="flex flex-col md:flex-row justify-between items-center"> <p class="text-gray-400 text-sm mb-4 md:mb-0">
本网站由 <span class="font-medium text-gray-300">领创工作室</span> 提供技术支持
</p> <div class="flex space-x-6"> <p class="mt-6 text-gray-400 text-sm">
&copy; 2020-2025 <span class="bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent font-medium">领创工作室</span>. 保留所有权利.
</p> </div> </div> </div> </div> </footer>  <script src="/js/main.js"></script>  <!-- Umami Analytics --> <script defer src="https://umami.lacs.cc/script.js" data-website-id="a4e8c20f-d2e8-4b10-bdf5-2d52c389fd45"></script> </body> </html>