@import "tailwindcss";

/* 自定义样式 */
@layer utilities {
  /* 容器间距 */
  .container {
    @apply px-4 sm:px-6 lg:px-8;
  }

  /* 统一卡片样式 */
  .unified-card {
    @apply bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden shadow-md transition-all duration-300;
    @apply hover:shadow-lg hover:-translate-y-1;
    @apply w-full h-full flex flex-col;
  }

  /* 卡片头部 */
  .card-header {
    @apply p-5 border-b border-gray-200 dark:border-gray-700 bg-gray-50/80 dark:bg-gray-900/50;
  }

  /* 卡片内容 */
  .card-body {
    @apply p-6 flex-grow;
  }

  /* 卡片底部 */
  .card-footer {
    @apply p-5 border-t border-gray-200 dark:border-gray-700 bg-gray-50/80 dark:bg-gray-900/50;
  }

  /* 标题样式 */
  h1, h2, h3, h4, h5, h6 {
    @apply text-gray-900 dark:text-white font-bold;
  }

  h1 { @apply text-4xl }
  h2 { @apply text-3xl }
  h3 { @apply text-2xl }
  h4 { @apply text-xl }
  h5 { @apply text-lg }
  h6 { @apply text-base }

  /* 正文样式 */
  p {
    @apply text-gray-700 dark:text-gray-300 leading-relaxed;
  }

  /* 按钮样式 */
  .btn-primary {
    @apply bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-all duration-300;
    @apply border-2 border-blue-600/20 flex items-center justify-center gap-2;
  }

  .btn-secondary {
    @apply bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 border-2 border-blue-600/30 px-6 py-3 rounded-lg font-medium;
    @apply hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:border-blue-600 transition-all duration-300;
    @apply flex items-center justify-center gap-2;
  }

  /* 表单控件 */
  input[type="text"], input[type="url"], input[type="email"], textarea {
    @apply w-full px-4 py-2 bg-white/80 dark:bg-gray-700/50 backdrop-blur-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent transition-all duration-300;
  }

  /* 图片样式 */
  img {
    @apply max-w-full h-auto rounded-lg;
  }

  /* 章节标题样式 */
  .section-header {
    @apply mb-10 pb-6 border-b border-gray-200 dark:border-gray-700;
  }

  .section-header h2 {
    @apply text-3xl font-bold text-blue-600 dark:text-blue-400;
  }

  /* 渐变文字 */
  .gradient-text {
    @apply bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent;
  }

  /* 科技感边框 */
  .tech-border {
    @apply relative;
  }

  .tech-border::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-lg blur-sm;
  }

  /* 科技感按钮 */
  .tech-button {
    @apply relative px-4 py-2 bg-gray-800/80 text-white rounded-lg border border-gray-600/50 backdrop-blur-sm;
    @apply hover:bg-gray-700/80 hover:border-blue-400/50 transition-all duration-300;
    @apply flex items-center justify-center gap-2 text-sm font-medium;
  }

  /* 科技感图标容器 */
  .tech-icon-container {
    @apply relative inline-flex items-center justify-center;
  }

  /* 玻璃效果卡片 */
  .glass-card {
    @apply bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50;
    @apply shadow-lg rounded-xl;
  }

  /* 3D卡片效果 */
  .card-3d {
    @apply transform-gpu transition-all duration-500;
    transform-style: preserve-3d;
  }

  .card-3d:hover {
    transform: translateY(-8px) rotateX(5deg) rotateY(5deg);
  }

  /* 悬浮按钮 */
  .floating-button {
    @apply transform transition-all duration-300 hover:scale-110;
  }

  /* 导航栏样式 */
  .nav-default {
    @apply bg-white/80 backdrop-blur-sm shadow-sm;
  }

  .nav-scrolled {
    @apply bg-white/95 backdrop-blur-md shadow-lg;
  }

  /* 网格背景 */
  .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* 打字机效果 */
  .typing-effect {
    @apply border-r-2 border-current;
    animation: typing 3.5s steps(40, end), blink-caret .75s step-end infinite;
    white-space: nowrap;
    overflow: hidden;
  }
}