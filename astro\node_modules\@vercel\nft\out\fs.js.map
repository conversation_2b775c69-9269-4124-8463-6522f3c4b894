{"version": 3, "file": "fs.js", "sourceRoot": "", "sources": ["../src/fs.ts"], "names": [], "mappings": ";;;;;;AACA,+BAA+B;AAC/B,8DAA6B;AAC7B,2CAAkC;AAElC,MAAM,UAAU,GAAG,qBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACxC,MAAM,UAAU,GAAG,qBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACxC,MAAM,MAAM,GAAG,qBAAE,CAAC,QAAQ,CAAC,IAAI,CAAC;AAEhC,MAAa,gBAAgB;IACnB,SAAS,CAAsC;IAC/C,SAAS,CAAqC;IAC9C,YAAY,CAAsC;IAClD,WAAW,CAAO;IAE1B,YAAY,EACV,KAAK,EACL,iBAAiB,GAQlB;QACC,IAAI,CAAC,WAAW,GAAG,IAAI,iBAAI,CAAC,iBAAiB,CAAC,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,KAAK,EAAE,SAAS,IAAI,IAAI,GAAG,EAAE,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,KAAK,EAAE,SAAS,IAAI,IAAI,GAAG,EAAE,CAAC;QAC/C,IAAI,CAAC,YAAY,GAAG,KAAK,EAAE,YAAY,IAAI,IAAI,GAAG,EAAE,CAAC;QAErD,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACjC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACjC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACzC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,IAAY;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,MAAM,KAAK,SAAS;YAAE,OAAO,MAAM,CAAC;QACxC,kFAAkF;QAClF,gDAAgD;QAChD,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACzE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QAE7C,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,IAAY;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,MAAM,KAAK,SAAS;YAAE,OAAO,MAAM,CAAC;QACxC,kFAAkF;QAClF,gDAAgD;QAChD,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACzE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QAE1C,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,IAAY;QACrB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,MAAM,KAAK,SAAS;YAAE,OAAO,MAAM,CAAC;QACxC,kFAAkF;QAClF,gDAAgD;QAChD,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACjE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAEtC,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,IAAY;QAC1C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,CAAC;YACpC,kCAAkC;YAClC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,KAAK;gBAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAA,cAAO,EAAC,IAAI,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS;gBACpE,MAAM,CAAC,CAAC;YACV,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,IAAY;QAC1C,IAAI,CAAC;YACH,OAAO,CAAC,MAAM,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC7C,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC/C,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,IAAY;QACtC,IAAI,CAAC;YACH,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CACzB,IAAY,EACZ,MAAyC;QAEzC,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAEjC,IAAI,CAAC;YACH,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACjC,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC;CACF;AA9GD,4CA8GC"}