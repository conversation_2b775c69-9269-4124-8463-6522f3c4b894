/**
 * City of the original client IP as calculated by Vercel Proxy.
 */
export declare const CITY_HEADER_NAME = "x-vercel-ip-city";
/**
 * Country of the original client IP as calculated by Vercel Proxy.
 */
export declare const COUNTRY_HEADER_NAME = "x-vercel-ip-country";
/**
 * Client IP as calculated by Vercel Proxy.
 */
export declare const IP_HEADER_NAME = "x-real-ip";
/**
 * Latitude of the original client IP as calculated by Vercel Proxy.
 */
export declare const LATITUDE_HEADER_NAME = "x-vercel-ip-latitude";
/**
 * Longitude of the original client IP as calculated by Vercel Proxy.
 */
export declare const LONGITUDE_HEADER_NAME = "x-vercel-ip-longitude";
/**
 * Country region of the original client IP calculated by Vercel Proxy.
 *
 * See [docs](https://vercel.com/docs/concepts/edge-network/headers#x-vercel-ip-country-region).
 */
export declare const REGION_HEADER_NAME = "x-vercel-ip-country-region";
/**
 * Postal code of the original client IP as calculated by Vercel Proxy.
 */
export declare const POSTAL_CODE_HEADER_NAME = "x-vercel-ip-postal-code";
/**
 * The request ID for each request generated by Vercel Proxy.
 */
export declare const REQUEST_ID_HEADER_NAME = "x-vercel-id";
/**
 * Unicode characters for emoji flags start at this number, and run up to 127469.
 */
export declare const EMOJI_FLAG_UNICODE_STARTING_POSITION = 127397;
/**
 * Represents the headers of a request.
 */
export interface Headers {
    get(name: string): string | null;
}
/**
 * We define a new type so this function can be reused with the global `Request`, `node-fetch` and other types.
 */
export interface Request {
    /**
     * Represents the headers of a request.
     */
    headers: Headers;
}
/**
 * The location information of a given request.
 */
export interface Geo {
    /** The city that the request originated from. */
    city?: string;
    /** The country that the request originated from. */
    country?: string;
    /** The flag emoji for the country the request originated from. */
    flag?: string;
    /** The [Vercel Edge Network region](https://vercel.com/docs/concepts/edge-network/regions) that received the request. */
    region?: string;
    /** The region part of the ISO 3166-2 code of the client IP.
     * See [docs](https://vercel.com/docs/concepts/edge-network/headers#x-vercel-ip-country-region).
     */
    countryRegion?: string;
    /** The latitude of the client. */
    latitude?: string;
    /** The longitude of the client. */
    longitude?: string;
    /** The postal code of the client */
    postalCode?: string;
}
/**
 * Returns the IP address of the request from the headers.
 *
 * @param {(Request|Headers)} input The incoming request object or headers.
 * @returns The IP address of the request.
 *
 * @example
 *
 * ```js
 * import { ipAddress } from '@vercel/functions';
 *
 * export function GET(request) {
 *   const ip = ipAddress(request);
 *   return new Response(`Your IP is ${ip}`);
 * }
 * ```
 *
 */
export declare function ipAddress(input: Request | Headers): string | undefined;
/**
 * Returns the location information for the incoming request.
 * @param request The incoming request object which provides the geolocation data
 * @returns The location information of the request, in this way:
 *
 * ```json
 * {
 *  "city": "New York",
 *  "country": "US",
 *  "flag": "🇺🇸",
 *  "countryRegion": "NY",
 *  "region": "iad1",
 *  "latitude": "40.7128",
 *  "longitude": "-74.0060"
 *  "postalCode": "10001"
 * }
 * ```
 *
 * @example
 *
 * ```js
 * import { geolocation } from '@vercel/functions';
 *
 * export function GET(request) {
 *   const details = geolocation(request);
 *   return Response.json(details);
 * }
 * ```
 */
export declare function geolocation(request: Request): Geo;
