---
import Layout from '../layouts/Layout.astro';
import Navigation from '../components/Navigation.astro';
import Footer from '../components/Footer.astro';
import FloatingButtons from '../components/FloatingButtons.astro';
import Modal from '../components/Modal.astro';

// 获取数据
const projectsResponse = await fetch(new URL('/json/projects_list.json', Astro.url));
const projects = await projectsResponse.json();

const sitesResponse = await fetch(new URL('/json/sites_list.json', Astro.url));
const sites = await sitesResponse.json();

const mediaPlatformsResponse = await fetch(new URL('/json/mediaPlatforms.json', Astro.url));
const mediaPlatforms = await mediaPlatformsResponse.json();

const contactInfoResponse = await fetch(new URL('/json/contactInfo.json', Astro.url));
const contactInfo = await contactInfoResponse.json();

const groupChatsResponse = await fetch(new URL('/json/groupChats.json', Astro.url));
const groupChats = await groupChatsResponse.json();
---

<Layout title="领创工作室-官方网站-lacs.cc">
  <Navigation />

  <!-- 英雄区域 -->
  <section class="pt-28 pb-16 min-h-screen flex items-center relative overflow-hidden">
    <!-- 粒子背景 -->
    <div id="particles-js" class="absolute inset-0 z-0"></div>

    <!-- 背景动效元素 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-10 -left-10 w-64 h-64 bg-primary-400/10 rounded-full blur-3xl animate-pulse"></div>
      <div class="absolute top-1/4 right-0 w-96 h-96 bg-secondary-400/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div>
      <div class="absolute bottom-20 left-1/3 w-80 h-80 bg-accent-400/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>

      <!-- 科技感网格背景 -->
      <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>

      <!-- 装饰线条 -->
      <div class="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary-400/30 to-transparent"></div>
      <div class="absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-secondary-400/30 to-transparent"></div>
    </div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="flex flex-col md:flex-row items-center justify-center">
        <!-- 科技感装饰元素 -->
        <div class="md:w-1/2 flex justify-center mt-10 md:mt-0 relative perspective-1000">
          <img src="/img/bg-lacs-group.webp"
               alt="领创工作室团队展示"
               class="max-w-full h-auto rounded-xl shadow-neon-primary animate-float object-cover transform transition-all duration-700 hover:scale-105 hover:shadow-neon-primary"
               style="max-height: 600px;">
          <!-- 添加科技感装饰元素 -->
          <div class="absolute -bottom-6 -left-6 w-24 h-24 bg-primary/10 rounded-full blur-2xl"></div>
          <div class="absolute -top-6 -right-6 w-32 h-32 bg-secondary/10 rounded-full blur-2xl"></div>
        </div>

        <div class="md:w-1/2 mb-8 md:mb-0 transform transition-all duration-700 hover:translate-y-[-10px] md:pl-6 lg:pl-10 xl:pl-14 md:pr-2 lg:pr-4 xl:pr-6">
          <h1 class="text-[clamp(2rem,5vw,3.5rem)] font-bold text-dark leading-tight mb-2">
            <span class="gradient-text">领创工作室</span><br>
            <span class="typing-effect text-primary-600 inline-block">Lead And Create Studio</span>
          </h1>
          <p class="mt-4 text-lg text-gray-600 max-w-xl opacity-90 hover:opacity-100 transition-opacity duration-300 border-l-4 border-primary-400/30 pl-4">
            我们致力于开发高质量的原创软件和工具，分享数码科技知识，为您带来更好的数字体验。
          </p>
          <div class="mt-8 flex flex-wrap gap-4">
            <a href="#projects-section"
               class="btn-primary px-6 py-3 rounded-lg flex items-center gap-2 shadow-neon transform hover:-translate-y-1 transition-all duration-300">
              <i class="fas fa-rocket"></i> 探索项目
            </a>
            <a href="#about"
               class="btn-secondary px-6 py-3 rounded-lg flex items-center gap-2 transform hover:-translate-y-1 transition-all duration-300">
              <i class="fas fa-info-circle"></i> 关于我们
            </a>
          </div>

          <!-- 技术标签 -->
          <div class="mt-8 flex flex-wrap gap-2">
            <span class="bg-primary-50 text-primary-700 text-xs px-3 py-1 rounded-full border border-primary-100">软件开发</span>
            <span class="bg-secondary-50 text-secondary-700 text-xs px-3 py-1 rounded-full border border-secondary-100">数码科技</span>
            <span class="bg-accent-50 text-accent-700 text-xs px-3 py-1 rounded-full border border-accent-100">创意设计</span>
            <span class="bg-gray-50 text-gray-700 text-xs px-3 py-1 rounded-full border border-gray-100">技术分享</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 向下滚动指示器 -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
      <a href="#projects-section" class="text-primary-600 hover:text-primary-800 transition-colors duration-300">
        <i class="fas fa-chevron-down text-2xl"></i>
      </a>
    </div>
  </section>

  <!-- 项目展示区域 -->
  <section id="projects-section" class="py-20 bg-gray-50 relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(37,117,252,0.1),transparent_50%),radial-gradient(circle_at_80%_20%,rgba(63,218,216,0.1),transparent_50%)]"></div>
    <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary-400/30 to-transparent"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="section-header text-center">
        <h2 class="text-4xl font-bold text-primary mb-4">我们的项目</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">
          探索我们精心开发的原创软件和工具，每一个项目都承载着我们对技术的热情和对用户体验的追求。
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {projects.map((project: any) => (
          <div class="unified-card card-3d group cursor-pointer"
               data-modal-title={project.name}
               data-modal-image={project.image}
               data-modal-description={project.description}>
            <div class="card-header">
              <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900 group-hover:text-primary transition-colors duration-300">
                  {project.name}
                </h3>
                <span class="text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded-full">
                  {project.category}
                </span>
              </div>
            </div>

            <div class="card-body">
              <div class="relative overflow-hidden rounded-lg mb-4 tech-border">
                <img src={project.image}
                     alt={project.name}
                     class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110">
                <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <i class="fas fa-search-plus text-white text-lg"></i>
                </div>
              </div>
              <p class="text-gray-600 text-sm line-clamp-3 group-hover:text-gray-800 transition-colors duration-300">
                {project.description}
              </p>
            </div>

            <div class="card-footer">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <span class="text-xs text-gray-500">
                    <i class="fas fa-calendar-alt mr-1"></i>
                    {project.date}
                  </span>
                </div>
                <div class="flex space-x-2">
                  {project.links?.map((link: any) => (
                    <a href={link.url}
                       target="_blank"
                       rel="noopener noreferrer"
                       class="tech-button text-xs px-3 py-1"
                       title={link.name}>
                      <i class={link.icon}></i>
                    </a>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- 分站展示区域 -->
  <section id="sites-section" class="py-20 bg-white relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(37,117,252,0.08),transparent_50%),radial-gradient(circle_at_20%_80%,rgba(63,218,216,0.08),transparent_50%)]"></div>
    <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-secondary-400/30 to-transparent"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="section-header text-center">
        <h2 class="text-4xl font-bold text-secondary mb-4">其他分站</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">
          发现我们的专业分站，每个分站都专注于特定领域，为您提供更专业、更深入的服务和内容。
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {sites.map((site: any) => (
          <div class="unified-card card-3d group">
            <div class="card-header">
              <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900 group-hover:text-secondary transition-colors duration-300">
                  {site.name}
                </h3>
                <span class="text-xs bg-secondary-100 text-secondary-700 px-2 py-1 rounded-full">
                  {site.category}
                </span>
              </div>
            </div>

            <div class="card-body">
              <div class="relative overflow-hidden rounded-lg mb-4 tech-border">
                <img src={site.image}
                     alt={site.name}
                     class="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110">
                <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <span class="bg-secondary/80 text-white text-xs px-2 py-1 rounded-full">
                    <i class="fas fa-external-link-alt mr-1"></i>
                    访问
                  </span>
                </div>
              </div>
              <p class="text-gray-600 text-sm line-clamp-3 group-hover:text-gray-800 transition-colors duration-300">
                {site.description}
              </p>
            </div>

            <div class="card-footer">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <span class="text-xs text-gray-500">
                    <i class="fas fa-globe mr-1"></i>
                    在线服务
                  </span>
                </div>
                <a href={site.url}
                   target="_blank"
                   rel="noopener noreferrer"
                   class="btn-secondary text-sm px-4 py-2"
                   data-umami-event={`访问分站-${site.name}`}>
                  <i class="fas fa-arrow-right mr-1"></i>
                  访问分站
                </a>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- 关于我们区域 -->
  <section id="about" class="py-20 bg-gray-50 relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_40%,rgba(37,117,252,0.1),transparent_50%),radial-gradient(circle_at_70%_60%,rgba(63,218,216,0.1),transparent_50%)]"></div>
    <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-accent-400/30 to-transparent"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="section-header text-center">
        <h2 class="text-4xl font-bold text-accent mb-4">关于我们</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">
          了解领创工作室的故事，我们的使命和愿景，以及我们如何通过技术创新为用户创造价值。
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div class="space-y-6">
          <div class="glass-card p-8">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-lightbulb text-primary-600 text-xl"></i>
              </div>
              <h3 class="text-2xl font-bold text-gray-900">我们的使命</h3>
            </div>
            <p class="text-gray-600 leading-relaxed">
              致力于开发高质量的原创软件和工具，分享数码科技知识，为用户带来更好的数字体验。我们相信技术应该让生活更美好，让工作更高效。
            </p>
          </div>

          <div class="glass-card p-8">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-rocket text-secondary-600 text-xl"></i>
              </div>
              <h3 class="text-2xl font-bold text-gray-900">我们的愿景</h3>
            </div>
            <p class="text-gray-600 leading-relaxed">
              成为数码科技领域的创新引领者，通过持续的技术创新和优质的产品服务，为全球用户提供卓越的数字化解决方案。
            </p>
          </div>

          <div class="glass-card p-8">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-users text-accent-600 text-xl"></i>
              </div>
              <h3 class="text-2xl font-bold text-gray-900">我们的团队</h3>
            </div>
            <p class="text-gray-600 leading-relaxed">
              由一群热爱技术、富有创造力的开发者组成。我们来自不同的背景，但都怀着同样的热情：用技术改变世界，用创新服务用户。
            </p>
          </div>
        </div>

        <div class="relative">
          <div class="relative overflow-hidden rounded-xl shadow-neon-accent">
            <img src="/img/bg-lacs-group.webp"
                 alt="领创工作室团队"
                 class="w-full h-auto object-cover transform transition-all duration-700 hover:scale-105">
            <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
          </div>

          <!-- 统计数据 -->
          <div class="grid grid-cols-2 gap-4 mt-8">
            <div class="glass-card p-6 text-center">
              <div class="text-3xl font-bold text-primary mb-2">10+</div>
              <div class="text-gray-600 text-sm">原创项目</div>
            </div>
            <div class="glass-card p-6 text-center">
              <div class="text-3xl font-bold text-secondary mb-2">5+</div>
              <div class="text-gray-600 text-sm">专业分站</div>
            </div>
            <div class="glass-card p-6 text-center">
              <div class="text-3xl font-bold text-accent mb-2">1000+</div>
              <div class="text-gray-600 text-sm">用户信赖</div>
            </div>
            <div class="glass-card p-6 text-center">
              <div class="text-3xl font-bold text-gray-700 mb-2">24/7</div>
              <div class="text-gray-600 text-sm">技术支持</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 联系我们区域 -->
  <section id="contact" class="py-20 bg-white relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(37,117,252,0.08),transparent_50%)]"></div>
    <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary-400/30 to-transparent"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="section-header text-center">
        <h2 class="text-4xl font-bold text-primary mb-4">联系我们</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">
          有任何问题或建议？我们很乐意听到您的声音。通过以下方式与我们取得联系。
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- 媒体平台 -->
        {mediaPlatforms.map((platform: any) => (
          <div class="glass-card p-6 text-center group hover:shadow-neon-primary transition-all duration-300">
            <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 transition-colors duration-300">
              <i class={`${platform.icon} text-primary-600 text-2xl`}></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">{platform.name}</h3>
            <p class="text-gray-600 text-sm mb-4">{platform.description}</p>
            <a href={platform.url}
               target="_blank"
               rel="noopener noreferrer"
               class="btn-primary text-sm px-4 py-2"
               data-umami-event={`访问-${platform.name}`}>
              <i class="fas fa-external-link-alt mr-1"></i>
              访问平台
            </a>
          </div>
        ))}

        <!-- 联系信息 -->
        {contactInfo.map((contact: any) => (
          <div class="glass-card p-6 text-center group hover:shadow-neon-secondary transition-all duration-300">
            <div class="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-secondary-200 transition-colors duration-300">
              <i class={`${contact.icon} text-secondary-600 text-2xl`}></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">{contact.name}</h3>
            <p class="text-gray-600 text-sm mb-4">{contact.description}</p>
            <a href={contact.url}
               class="btn-secondary text-sm px-4 py-2"
               data-umami-event={`联系-${contact.name}`}>
              <i class="fas fa-envelope mr-1"></i>
              立即联系
            </a>
          </div>
        ))}

        <!-- 群聊信息 -->
        {groupChats.map((group: any) => (
          <div class="glass-card p-6 text-center group hover:shadow-neon-accent transition-all duration-300">
            <div class="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-accent-200 transition-colors duration-300">
              <i class={`${group.icon} text-accent-600 text-2xl`}></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">{group.name}</h3>
            <p class="text-gray-600 text-sm mb-4">{group.description}</p>
            <button class="btn-primary text-sm px-4 py-2"
                    data-modal-title={group.name}
                    data-modal-image={group.qrcode}
                    data-modal-description={`扫描二维码加入${group.name}群聊`}>
              <i class="fas fa-qrcode mr-1"></i>
              查看二维码
            </button>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- 引入组件 -->
  <Footer />
  <FloatingButtons />
  <Modal />

  <!-- JavaScript -->
  <script src="/js/main.js" is:inline></script>
</Layout>
